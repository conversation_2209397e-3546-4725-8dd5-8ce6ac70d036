<?php

use App\Http\Controllers\Api\AreaStucture\SearchController;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\Password\ResetController;
use App\Http\Controllers\Api\Auth\RegistrationController;
use App\Http\Controllers\Api\NotificationApiController;
use App\Http\Controllers\Api\PosApiController;
use App\Http\Controllers\Api\Profile\DetailsController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\Users\SearchController as UsersSearchController;

Route::prefix('v1')->name('api.')->group(function () {
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
    Route::get('/server-datetime', [SettingsController::class, 'getServerDateTime']);
    Route::group(['middleware' => ['guest']], function () {
		Route::post('/check-first-login', [AuthController::class, 'checkFirstLogin']);

        Route::prefix('/login')->group(function () {
            Route::post('/dms', [AuthController::class, 'handleLogin'])->name('dmsLogin');
            Route::post('/vietlott', [AuthController::class, 'handleLogin'])->name('vietlottLogin');
            Route::post('/required-otp', [AuthController::class, 'loginByOtp'])->name('otpLogin');
            Route::post('/login-by-otp', [AuthController::class, 'confirmOtp'])->name('confirmOtp');
            //  Route::post('/forgot-password/otp', [AuthController::class, 'requestForgotPassword'])->name('forgotPassword');
        });

    });

    if (setting('reg_enabled')) {
        Route::middleware(['guest'])->group(function () {
            Route::post('/register', [RegistrationController::class, 'index'])->name('register');
        });
        Route::middleware(['auth', 'registration'])->group(function () {
            Route::post('password/reset', [ResetController::class, 'index']);
            // Route::post('email/resend', 'Auth\VerificationController@resend');
            // Route::post('email/verify', 'Auth\VerificationController@verify');
        });
    }

    Route::group(['middleware' => ['auth']], function () {

        Route::post('/notification-settings', [SettingsController::class, 'notificationSettings']);
        
        Route::post('logout', 'Auth\AuthController@logout');

        Route::post('/change-password', [AuthController::class, 'changePassword']);
        Route::post('/first-time-password-change', [AuthController::class, 'changeFirstTimePassword']);
		
        Route::post('/update-verify-first-login', [AuthController::class, 'updateVerifyFirstLogin']);

    });

    Route::group(['middleware' => ['auth', 'verified']], function () {
        Route::group(['prefix' => 'me'], function () {
            Route::get('/', [DetailsController::class, 'index']);
            Route::patch('/details', [DetailsController::class, 'update']);
            Route::patch('/details/auth', [\App\Http\Controllers\Api\Profile\AuthDetailsController::class, 'update']);

            Route::post('/avatar', [\App\Http\Controllers\Api\Profile\AvatarController::class, 'update']);
            Route::delete('/avatar', [\App\Http\Controllers\Api\Profile\AvatarController::class, 'destroy']);
            Route::put('/avatar/external', [\App\Http\Controllers\Api\Profile\AvatarController::class, 'updateExternal']);


            Route::post('/delete-account', [AuthController::class, 'deleteAccount']);
            Route::group(['prefix' => '/notifications'], function () {
                Route::get('/', [NotificationApiController::class, 'index']);
                Route::get('/mark-as-read', [NotificationApiController::class, 'markAsRead']);
                Route::delete('/{notification}', [NotificationApiController::class, 'destroy']);
                Route::post('/delete/multiple', [NotificationApiController::class, 'deleteMultiple']);
                Route::delete('/', [NotificationApiController::class, 'deleteAll']);
            });
        });

//        Route::group(['middleware' => 'two-factor'], function () {
//            Route::put('me/2fa', 'Profile\TwoFactorController@update');
//            Route::post('me/2fa/verify', 'Profile\TwoFactorController@verify');
//            Route::delete('me/2fa', 'Profile\TwoFactorController@destroy');
//        });


        Route::get('/branches-search', [SearchController::class, 'searchBranches'])->name('searchBranches');
        Route::get('/agencies-search', [SearchController::class, 'searchAgencies'])->name('searchAgencies');
        Route::get('/pos-search', [SearchController::class, 'searchPos'])->name('searchPos');


        Route::get('/province-search', [SearchController::class, 'searchProvinces'])->name('searchProvinces');
        Route::get('/district-search', [SearchController::class, 'searchDistricts'])->name('searchDistricts');
        Route::get('/ward-search', [SearchController::class, 'searchWards'])->name('searchWards');


        Route::get('/user-search', [SearchController::class, 'searchUsers'])->name('searchUsers');
        Route::get('/search-user', [UsersSearchController::class, 'SearchUser'])->name('SearchUser');


        Route::group(['prefix' => '/pos'], function () {


            Route::get('/', [PosApiController::class, 'index']);

            Route::get('{pos_id}/show', [PosApiController::class, 'show']);

//            Route::get('pos-checkin-statuses', [PosApiController::class, 'listCheckinStatuses']);
//            Route::get('pos-checkin-warn', [PosApiController::class, 'listCheckinWarn']);

            Route::get('pos-types', [PosApiController::class, 'posType']);

            Route::get('pos-updates/pending/{posId}', [PosApiController::class, 'getPendingUpdates']);

            Route::get('store-closure-reasons', [PosApiController::class, 'storeClosureReasons']);
            Route::get('pos-readiness-checklists', [PosApiController::class, 'posChecklists']);

            Route::post('propose-lat-lon', [PosApiController::class, 'proposeLatLon']);
            Route::post('approve-lat-lon', [PosApiController::class, 'approveLatLon'])->middleware('pos.update');
            Route::post('reject-lat-lon', [PosApiController::class, 'rejectLatLon'])->middleware('pos.update');

            Route::group(['prefix' => '{pos_id}'], function () {
                Route::get('/visit-history', [PosApiController::class, 'getVisitHistory']);
                Route::get('/visit/{visitId}', [PosApiController::class, 'getVisitDetail'])->name('getVisitDetail');
            });

        });
    });
});
