<?php

return [
    'new_permission' => 'Neue Berechtigung mit dem Namen :name wurde erstellt.',
    'updated_permission' => 'Berechtigung mit dem Namen :name wurde aktualisiert.',
    'deleted_permission' => 'Berechtigung mit dem Namen :name wurde gelöscht.',

    'new_role' => 'Neue Benutzerrolle mit dem Namen :name wurde erstellt.',
    'updated_role' => 'Benutzerrolle mit dem Namen :name wurde aktualisiert.',
    'deleted_role' => 'Benutzerrolle mit dem Namen :name wurde gelöscht.',
    'updated_role_permissions' => 'Rollen Benutzerberechtigungen wurden aktualisiert.',

    'logged_in' => 'Angemeldet.',
    'logged_out' => 'Abgemeldet.',
    'created_account' => 'Ein Benutzerkonto wurde erstellt.',
    'updated_avatar' => 'Der Profil-Avatar wurde aktualisiert.',
    'updated_profile' => 'Die Profil-Details wurden aktualisiert.',
    'deleted_user' => 'Benutzer :name wurde gelöscht.',
    'banned_user' => 'Benutzer :name wurde gesperrt.',
    'updated_profile_details_for' => 'Profildetails für Benutzer :name wurden aktualisiert.',
    'created_account_for' => 'Ein Benutzerkonto für Benutzer :name wurde erstellt.',
    'updated_settings' => 'WebSeiten Einstellungen wurden aktualisiert.',
    'enabled_2fa' => 'Zwei-Faktor Authentifizierung wurde aktiviert.',
    'disabled_2fa' => 'Zwei-Faktor Authentifizierung wurde deaktiviert.',
    'enabled_2fa_for' => 'Zwei-Faktor Authentifizierung für Benutzer :name wurde aktiviert.',
    'disabled_2fa_for' => 'Zwei-Faktor Authentifizierung für Benutzer :name wurde deaktiviert.',
    'requested_password_reset' => 'Mail zum Zurücksetzen des Passworts wurde angefordert.',
    'reseted_password' => 'Das Passwort wurde mit Hilfe der Option "Passwort vergessen" zurückgesetzt.',

    'started_impersonating' => 'Gestartet Identitätswechsel Benutzer :name (ID: :id)',
    'stopped_impersonating' => 'Gestoppt Identitätswechsel Benutzer :name (ID: :id)',
];
