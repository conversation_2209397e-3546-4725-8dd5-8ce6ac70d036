@extends('layouts.app')

@section('page-title', 'Phân giao Tỉnh/Thành phố')
@section('page-heading', $branch->name)
@php
    $hideSidebar = true;
@endphp
@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        <a href="{{ route('branches.index') }}" class="text-primary text-hover-primary">
            Thiết lập
        </a>
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Đ<PERSON><PERSON><PERSON> bán
    </li> <!--end::Item-->
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Chi nhánh {{ $branch->name }}
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">Phân giao Tỉnh/Thành phố</li>

@stop

@section('style')
    {!! HTML::style(asset('assets/plugins/custom/datatables/datatables.bundle.css')) !!}
@endsection

@section('content')

    <!--begin::Content-->
    <div id="kt_app_content" class="app-content">

        <!--begin::report-->
        <div class="row gx-6 gx-xl-9 mb-10">
            <div class="col-lg-6 col-xxl-4">
                <!--begin::Card-->
                <div class="card h-100">
                    <!--begin::Card body-->
                    <div class="card-body p-9">
                        <!--begin::Heading-->
                        <div class="fs-2hx fw-bold" data-kt-countup="true"
                            data-kt-countup-value="{{ count($branch->pos) }}">0
                        </div>
                        <div class="fs-4 fw-semibold text-gray-500 mb-7">Tổng số điểm bán</div>
                        <!--end::Heading-->
                        <!--begin::Wrapper-->
                        <div class="d-flex flex-wrap">
                            <!--begin::Chart-->
                            <div class="d-flex flex-center h-100px w-100px me-9 mb-5">
                                <canvas id="kt_branch_list_chart"></canvas>
                            </div>
                            <!--end::Chart-->
                            <!--begin::Labels-->
                            <div class="d-flex flex-column justify-content-center flex-row-fluid pe-11 mb-5">
                                <!--begin::Label-->
                                {{--                                <div class="d-flex fs-6 fw-semibold align-items-center mb-3"> --}}
                                {{--                                    <div class="bullet bg-primary me-3"></div> --}}
                                {{--                                    <div class="text-gray-500">Đã lên lịch</div> --}}
                                {{--                                    <div class="ms-auto fw-bold text-gray-700">30</div> --}}
                                {{--                                </div> --}}
                                <!--end::Label-->
                                <!--begin::Label-->
                                <div class="d-flex fs-6 fw-semibold align-items-center mb-3">
                                    <div class="bullet bg-success me-3"></div>
                                    <div class="text-gray-500">Đã ghé thăm</div>
                                    <div class="ms-auto fw-bold text-gray-700">10</div>
                                </div>
                                <!--end::Label-->
                                <!--begin::Label-->
                                <div class="d-flex fs-6 fw-semibold align-items-center">
                                    <div class="bullet bg-gray-300 me-3"></div>
                                    <div class="text-gray-500">Chưa ghé thăm</div>
                                    <div class="ms-auto fw-bold text-gray-700">5</div>
                                </div>
                                <!--end::Label-->
                            </div>
                            <!--end::Labels-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Card-->
            </div>
            <div class="col-lg-6 col-xxl-4">
                <!--begin::Budget-->
                <div class="card h-100">
                    <div class="card-body p-9">
                        <div class="fs-2hx fw-bold" data-kt-countup="true" data-kt-countup-value="9990000"
                            data-kt-countup-suffix="đ">0
                        </div>
                        <div class="fs-4 fw-semibold text-gray-500 mb-7">Tổng doanh thu</div>
                        <div class="fs-6 d-flex justify-content-between mb-4">
                            <div class="fw-semibold">Hải phòng</div>
                            <div class="d-flex fw-bold">
                                <i class="ki-duotone ki-arrow-up-right fs-3 me-1 text-success">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>6,570
                            </div>
                        </div>
                        <div class="separator separator-dashed"></div>
                        <div class="fs-6 d-flex justify-content-between my-4">
                            <div class="fw-semibold">Hà nội</div>
                            <div class="d-flex fw-bold">
                                <i class="ki-duotone ki-arrow-down-left fs-3 me-1 text-danger">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>408
                            </div>
                        </div>
                        <div class="separator separator-dashed"></div>
                        <div class="fs-6 d-flex justify-content-between mt-4">
                            <div class="fw-semibold">Vĩnh phúc</div>
                            <div class="d-flex fw-bold">
                                <i class="ki-duotone ki-arrow-up-right fs-3 me-1 text-success">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>920
                            </div>
                        </div>
                    </div>
                </div>
                <!--end::Budget-->
            </div>
            <div class="col-lg-6 col-xxl-4">
                <!--begin::Clients-->
                <div class="card h-100">
                    <div class="card-body p-9">
                        <!--begin::Heading-->
                        <div class="fs-2hx fw-bold">{{ $staffs->count() }}</div>
                        <div class="fs-4 fw-semibold text-gray-500 mb-7">Chuyên viên kinh doanh</div>
                        <!--end::Heading-->
                        <!--begin::Users group-->
                        <div class="symbol-group symbol-hover mb-9">
                            <div class=" mb-9">
                                @include('partials.symbol-group-user', ['listUsers' => @$staffs])
                            </div>
                        </div>
                        <!--end::Users group-->
                        {{--  <!--begin::Actions-->
                          <div class="d-flex">
                              <a href="#" class="btn btn-primary btn-sm me-3" data-bs-toggle="modal" data-bs-target="#kt_modal_view_users">All Clients</a>
                              <a href="#" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#kt_modal_users_search">Invite New</a>
                          </div>
                          <!--end::Actions--> --}}
                    </div>
                </div>
                <!--end::Clients-->
            </div>
        </div>
        <!--end::report-->


        <!--begin::Form-->
        <div class="d-flex flex-column flex-lg-row">
            <!--begin::Aside column-->
            <form id="kt_branch_edit_form" class="form d-flex flex-column gap-7 gap-lg-10 w-100 w-lg-300px mb-7 me-lg-10"
                action="{{ route('branches.update', $branch->id) }}">
                <!--begin::Status-->
                <!--begin::Template settings-->
                <div class="card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h2>Thông tin</h2>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Select store template-->
                        <label for="start_date" class="form-label ">Tên chi nhánh</label>
                        <!--end::Select store template-->
                        <!--begin::Input group-->
                        <div class="d-flex align-items-center justify-content-end flex-equal order-3 fw-row"
                            data-bs-toggle="tooltip" title="Tháng áp dụng">
                            <!--begin::Input-->
                            <div class="position-relative d-flex  w-100">
                                <input class="form-control " placeholder="Tên của chi nhánh" disabled
                                    name="name" value="{{ @$branch->name ?? '' }}" />
                            </div>
                            <!--end::Input-->
                        </div>
                        <!--end::Input group-->
                    </div>

                    <div class="card-body pt-0">
                        <!--begin::Select store template-->
                        <label for="start_date" class="form-label ">Mã chi nhánh</label>
                        <!--end::Select store template-->
                        <!--begin::Input group-->
                        <div class="d-flex align-items-center justify-content-end flex-equal order-3 fw-row"
                            data-bs-toggle="tooltip" title="Tháng áp dụng">
                            <!--begin::Input-->
                            <div class="position-relative d-flex align-items-center w-100">
                                <input class="form-control " placeholder="Tên của chi nhánh" disabled
                                    name="code" value="{{ @$branch->code ?? '' }}" />
                            </div>
                            <!--end::Input-->
                        </div>
                        <!--end::Input group-->
                    </div>

                    <!--end::Card body-->
                </div>
                <!--end::Template settings-->

            </form>
            <!--end::Aside column-->

            <!--begin::Main column-->
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                <!--begin:::Tabs-->
                <!--end:::Tabs-->
                <!--begin::Tab content-->
                <!--begin::Tab pane create-->
                <!--end::Tab pane-->
                <!--end::Form chi cho tab general-->
                <!--begin::Tab pane create-->
                <div class="tab-pane" id="assign">
                    <div class="d-flex flex-column gap-7 gap-lg-10">

                        <div class="card">
                            <!--begin::Card header-->
                            <form action="" method="GET">
                                <div class="card-header border-0 pt-6">
                                    <!--begin::Card title-->
                                    <div class="card-title">
                                        <!--begin::Search-->
                                        <div class="d-flex align-items-center position-relative my-1">
                                            <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            <input type="text" name="search" value="{{ @$filters['search'] }}"
                                                class="form-control w-250px ps-13 w-ms-100"
                                                placeholder="Nhập tìm kiếm" />
                                        </div>

                                        <!--end::Search-->
                                    </div>
                                    <!--begin::Card title-->
                                    <!--begin::Card toolbar-->
                                    <div class="card-toolbar">
                                        <!--begin::Toolbar-->

                                        @permission('branch.update')
                                            <button type="button" class="btn btn-primary me-3" data-bs-toggle="modal"
                                                data-bs-target="#kt_modal_assign_province">Phân giao Tỉnh/Thành phố
                                                <span class="ms-2" data-bs-toggle="tooltip"
                                                    title="Phân giao Tỉnh/Thành phố cho chi nhánh này">
                                                    <i class="ki-duotone ki-information fs-7">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                        <span class="path3"></span>
                                                    </i>
                                                </span>
                                            </button>
                                        @endpermission


                                        <div class="d-flex justify-content-end" data-kt-pos-table-toolbar="base">

                                            {{--   <!--begin::Filter-->
                                               <button type="button" class="btn btn-light-primary " data-kt-menu-trigger="click"
                                                       data-kt-menu-placement="bottom-end">
                                                   <i class="ki-duotone ki-filter fs-2">
                                                       <span class="path1"></span>
                                                       <span class="path2"></span>
                                                   </i>{{ __('Filter') }}
                                               </button>
                                               <div class="menu menu-sub menu-sub-dropdown" data-kt-menu="true">
                                                   <div class="px-7 py-5">
                                                       <div class="fs-5 text-gray-900 fw-bold">{{ __('Filter Options') }}</div>
                                                   </div>
                                                   <div class="separator border-gray-200"></div>
                                                   <div class="px-7 py-5" data-kt-pos-table-filter="form">
                                                       <div class="row">
                                                           <div class="col">
                                                               <div class="mb-10 w-300px w-md-325px">
                                                                   @include('partials.filters.location-filter',['spa'=>true,'label' => 'Lọc theo Tỉnh/Thành phố','hiddenArea'=>true])
                                                               </div>

                                                           </div>
                                                           <div class="col">
                                                               <div class="w-300px w-md-325px">

                                                                   <!--begin::Input group-->
                                                                   @include('partials.filters.structure-filter',['spa'=>true,'label' => 'Lọc theo Chi nhánh/đại lý','hiddenPos'=>true])
                                                                   <!--end::Input group-->
                                                               </div>

                                                           </div>
                                                       </div>

                                                       <!--begin::Actions-->
                                                       <div class="d-flex justify-content-end">
                                                           <a href="{{ route('branch.assignPosTobranch',$branch->id) }}" type="reset"
                                                              class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6">{{ __('Reset') }}</a>
                                                           <button type="submit"
                                                                   class="btn btn-primary fw-semibold px-6">{{ __('Apply') }}</button>
                                                       </div>
                                                       <!--end::Actions-->
                                                   </div>


                                                   <!--end::Content-->
                                               </div>
                                               <!--end::Menu 1-->
                                               <!--end::Filter-->
                           --}}

                                        </div>
                                        <!--end::Toolbar-->
                                        <!--begin::Group actions-->
                                        <div class="d-flex justify-content-end align-items-center d-none"
                                            data-kt-pos-table-toolbar="selected">

                                            <div class="fw-bold me-5">
                                                <span class="me-2"
                                                    data-kt-pos-table-select="selected_count"></span>{{ __('Selected') }}
                                            </div>
                                            <button type="button" class="btn btn-info" {{--                                                    data-assign-multiple="{{ route('branch.assignPosTobranch',$branch->id) }}" --}}
                                                id="assign-multiple-pos" data-kt-pos-table-select="assign_selected">Phân
                                                giao
                                            </button>

                                        </div>
                                        <!--end::Group actions-->
                                    </div>
                                    <!--end::Card toolbar-->
                                </div>
                            </form>
                            <!--end::Card header-->
                            <!--begin::Card body-->
                            <div class="card-body py-4 table-content  ">
                                <!--begin::Table-->
                                <table class="table align-middle table-row-dashed fs-6 gy-5 min-h-500px"
                                    id="kt_table_province">
                                    <thead>
                                        <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                            {{-- <th class="w-10px pe-2">
                                             <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                 <input class="form-check-input" type="checkbox" data-kt-check="true"
                                                        data-kt-check-target="#kt_table_province .form-check-input" value=""/>
                                             </div>
                                         </th> --}}
                                            <th>CODE</th>
                                            <th>Tỉnh/Thành phố</th>
                                            <th>Tổng POS</th>
                                            <th class="min-w-125px">
                                                Số ĐBH ghé thăm tối thiểu
                                                <span data-bs-toggle="tooltip" data-bs-placement="top"
                                                    title="Số ĐBH ghé thăm tối thiểu áp dụng cho việc kế hoạch Kiểm tra, đánh giá định kỳ hàng tháng mỗi Tỉnh/Thành phố">

                                                    <i class="ki-duotone ki-information">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                        <span class="path3"></span>
                                                    </i>
                                                </span>
                                            </th>
                                            <th class="text-end min-w-100px">{{ __('Actions') }}</th>

                                        </tr>
                                    </thead>
                                    <tbody class="text-gray-600 fw-semibold " id="kt_tbody_pos">
                                        @foreach ($branch->provinces as $province)
                                            <tr>
                                                <td>{{ $province->locationCode }}</td>

                                                <td>{{ $province->locationName ?? '' }}</td>
                                                <td>{{ $province->provincePos->count() ?? '' }}</td>
                                                <td>
                                                    <div class="input-group mb-5">
                                                        <input type="number"
                                                            placeholder="Số điểm bán ghé thăm tối thiểu trong một tháng"
                                                            value="{{ $province->pivot->min_visit }}"
                                                            data-province-code="{{ $province->locationCode }}"
                                                            data-branch-code="{{ $branch->code }}"
                                                            class="form-control min-visit-input mw-25"
                                                            aria-label="Recipient's username"
                                                            aria-describedby="basic-addon2" disabled />

                                                        <span class="input-group-text edit-icon  cursor-pointer"
                                                            data-bs-toggle="tooltip"
                                                            title="Chỉnh sửa số điểm bán ghé thăm tối thiểu trong một tháng"
                                                            style="border-top-right-radius: 2.1rem;border-bottom-right-radius: 2.1rem;">
                                                            <i class="ki-duotone ki-pencil">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                            </i>
                                                        </span>
                                                        <span class="input-group-text save-icon d-none cursor-pointer"
                                                            data-bs-toggle="tooltip" title="Lưu">
                                                            <i class="ki-duotone ki-save-deposit  text-success">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                                <span class="path3"></span>
                                                                <span class="path4"></span>
                                                            </i>
                                                        </span>


                                                    </div>

                                                </td>
                                                <td class="text-end">
                                                    @permission('branch.update')
                                                        <form method="POST"
                                                            action="{{ route('branches.removeProvinceAssign', ['branchCode' => $branch->code, 'provinceCode' => $province->locationCode]) }}"
                                                            id="delete-item-form-{{ $branch->code }}">
                                                            @csrf
                                                            <a href="#"
                                                                class="btn btn-color-muted btn-active-color-danger "
                                                                data-bs-toggle="tooltip" data-bs-placement="top"
                                                                title="Xóa Tỉnh/Thành phố khỏi chi nhánh này"
                                                                data-method-cb="reload"
                                                                id="delete-pos-assign-{{ $branch->code }}"
                                                                data-name="{{ $province->locationCode }}"
                                                                data-kt-table-action="delete_row">
                                                                <i class="ki-duotone ki-trash-square fs-1 ">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                    <span class="path4"></span>
                                                                </i>
                                                            </a>
                                                        </form>
                                                    @endpermission
                                                </td>
                                            </tr>
                                        @endforeach

                                    </tbody>
                                </table>

                            </div>


                            <!--end::Card body-->
                        </div>


                    </div>
                </div>
                <div class="d-flex justify-content-end">
                    <!--begin::Button-->
                    {{--   <a href="{{ route('branches.index') }}" id="kt_update_branch_cancel"
                          class="btn btn-light me-5">Hủy</a>
                       <!--end::Button-->
                       <!--begin::Button-->
                       <button type="submit" id="kt_branch_submit" class="btn btn-primary me-5">
                           <span class="indicator-label">Lưu</span>
                           <span class="indicator-progress">Please wait...
                                                           <span
                                                               class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                       </button> --}}

                    {{-- <button type="submit" id="kt_branch_assign_submit" class="btn btn-info">
                         <span class="indicator-label">Lưu và phân giao điểm bán</span>
                         <span class="indicator-progress">Please wait...
                                                         <span
                                                             class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                     </button> --}}
                    <!--end::Button-->
                </div>
            </div>
            <!--end::Main column-->
        </div>


    </div>
    <!--end::Content-->
@stop
@push('section-modal')
    <!--begin::Modal - Invite Friends-->
    <div class="modal fade" id="kt_modal_assign_province" tabindex="-1" aria-hidden="true">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-fullscreen">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-end">
                    <!--begin::Close-->
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <i class="ki-duotone ki-cross fs-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                    <!--end::Close-->
                </div>
                <!--begin::Modal header-->
                <!--begin::Modal body-->
                <!--begin::Heading-->

                <!--end::Heading-->
                <div class="modal-body ">
                </div>
                <!--end::Modal body-->

            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    <!--end::Modal - Invite Friend-->
@endpush


@section('script-vendor')
    {!! HTML::script('assets/plugins/custom/datatables/datatables.bundle.js') !!}
@stop
@section('script')
    <script>
        "use strict";

        // Class definition
        var KTBranchList = function() {
            var initChart = function() {
                // init chart
                var element = document.getElementById("kt_branch_list_chart");

                if (!element) {
                    return;
                }

                var config = {
                    type: 'doughnut',
                    data: {
                        datasets: [{
                            data: [30, 45, 25],
                            backgroundColor: ['#00A3FF', '#50CD89', '#E4E6EF']
                        }],
                        labels: ['Active', 'Completed', 'Yet to start']
                    },
                    options: {
                        chart: {
                            fontFamily: 'inherit'
                        },
                        borderWidth: 0,
                        cutout: '75%',
                        cutoutPercentage: 65,
                        responsive: true,
                        maintainAspectRatio: false,
                        title: {
                            display: false
                        },
                        animation: {
                            animateScale: true,
                            animateRotate: true
                        },
                        stroke: {
                            width: 0
                        },
                        tooltips: {
                            enabled: true,
                            intersect: false,
                            mode: 'nearest',
                            bodySpacing: 5,
                            yPadding: 10,
                            xPadding: 10,
                            caretPadding: 0,
                            displayColors: false,
                            backgroundColor: '#20D489',
                            titleFontColor: '#ffffff',
                            cornerRadius: 4,
                            footerSpacing: 0,
                            titleSpacing: 0
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                };

                var ctx = element.getContext('2d');
                var myDoughnut = new Chart(ctx, config);
            }


            // Public methods
            return {
                init: function() {
                    initChart();
                }
            }
        }();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTBranchList.init();

        });
    </script>

    <!-- JavaScript -->
    <script>
        "use strict";

        // Class definition
        var KTAppSavebranch = function() {

            const initFlatpickr = () => {
                const form = document.getElementById('kt_branch_edit_form');
                const month = $(form.querySelector('[name="month"]'));
                month.flatpickr({
                    locale: "vn",
                    plugins: [
                        monthSelectPlugin({
                            shorthand: true, //defaults to false
                            dateFormat: "Y-m", //defaults to "F Y"
                            altFormat: "F Y", //defaults to "F Y"
                            theme: "light" // defaults to "light"
                        })
                    ],
                    altInput: true,
                    altFormat: "F Y", // Định dạng hiển thị cho người dùng
                    dateFormat: "Y-m", // Định dạng lưu trữ
                    defaultDate: new Date(),
                })

            }

            const initQuill = () => {
                // Define all elements for quill editor
                const elements = [
                    '#kt_add_branch_description',
                ];

                // Loop all elements
                elements.forEach(element => {
                    // Get quill element
                    let quill = document.querySelector(element);

                    // Break if element not found
                    if (!quill) {
                        return;
                    }

                    // Init quill --- more info: https://quilljs.com/docs/quickstart/
                    quill = new Quill(element, {
                        modules: {
                            toolbar: [
                                [{
                                    header: [1, 2, false]
                                }],
                                ['bold', 'italic', 'underline'],
                                ['image', 'code-block']
                            ]
                        },
                        placeholder: 'Type your text here...',
                        theme: 'snow' // or 'bubble'
                    });

                    quill.on('text-change', function() {
                        document.getElementById('description').value = quill.root.innerHTML;
                    });

                    // Đặt giá trị ban đầu cho Quill từ input hidden
                    quill.root.innerHTML = document.getElementById('description').value;
                });
            }

            // Init tagify

            // Init form repeater --- more info: https://github.com/DubFriend/jquery.repeater

            // Category status handler
            const handleStatus = () => {
                const target = document.getElementById('kt_branch_status');
                const select = document.getElementById('kt_branch_status_select');
                const statusClasses = ['bg-success', 'bg-warning', 'bg-danger'];

                $(document).on('change', select, function(e) {
                    const value = e.target.value;

                    switch (value) {
                        case "1": {
                            target.classList.remove(...statusClasses);
                            target.classList.add('bg-success');
                            break;
                        }
                        case "0": {
                            target.classList.remove(...statusClasses);
                            target.classList.add('bg-danger');
                            break;
                        }

                        default:
                            break;
                    }
                });


            }


            // Submit form handler
            const handleSubmit = () => {
                // Define variables
                let validator;

                // Get elements
                const form = document.getElementById('kt_branch_edit_form');
                const submitButton = document.getElementById('kt_branch_submit');
                const submitAssignButton = document.getElementById('kt_branch_assign_submit');

                validator = FormValidation.formValidation(
                    form, {
                        fields: {
                            'name': {
                                validators: {
                                    notEmpty: {
                                        message: 'Trường bắt buộc'
                                    }
                                }
                            },
                            'month': {
                                validators: {
                                    notEmpty: {
                                        message: 'Trường bắt buộc'
                                    }
                                }
                            },
                            'staff_id': {
                                validators: {
                                    notEmpty: {
                                        message: 'Trường bắt buộc'
                                    }
                                }
                            },

                        },
                        plugins: {
                            trigger: new FormValidation.plugins.Trigger(),
                            bootstrap: new FormValidation.plugins.Bootstrap5({
                                rowSelector: '.fv-row',
                                eleInvalidClass: '',
                                eleValidClass: ''
                            })
                        }
                    }
                );


                const handleSubmitSave = (action = "update") => {
                    if (validator) {
                        validator.validate().then(function(status) {
                            if (status === 'Valid') {
                                submitButton.setAttribute('data-kt-indicator', 'on');

                                // Disable submit button whilst loading
                                submitButton.disabled = true;
                                const formData = new FormData(form);
                                formData.append('action', action);

                                formData.append('_method', 'PUT');
                                axios.post(form.action, formData)
                                    .then(response => {
                                        const data = response.data;
                                        submitButton.removeAttribute('data-kt-indicator');
                                        submitButton.disabled = false;
                                        // Handle success
                                        if (data.success) {
                                            KTApp.notify({
                                                title: 'Cập nhật',
                                                text: data.message ||
                                                    "branch has been successfully updated!"
                                            })
                                            window.location.href = data
                                            .redirectUrl; // Redirect người dùng

                                        } else {
                                            KTApp.notify({
                                                icon: 'error',
                                                title: 'Cập nhật',
                                                text: data.message ||
                                                    "An error occurred. Please try again."
                                            })
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error:', error);
                                        submitButton.removeAttribute('data-kt-indicator');
                                        submitButton.disabled = false;
                                        if (error.response && error.response.data && error.response
                                            .data.errors) {
                                            const errors = error.response.data.errors;
                                            let errorMessage = '';

                                            for (const key in errors) {
                                                if (errors.hasOwnProperty(key)) {
                                                    errorMessage += `${errors[key][0]}\n`;
                                                }
                                            }

                                            Swal.fire({
                                                text: errorMessage,
                                                icon: "error",
                                                buttonsStyling: false,
                                                confirmButtonText: "Ok",
                                                customClass: {
                                                    confirmButton: "btn btn-primary"
                                                }
                                            });
                                        } else {
                                            Swal.fire({
                                                text: "Có lỗi xảy ra, vui lòng thử lại sau.",
                                                icon: "error",
                                                buttonsStyling: false,
                                                confirmButtonText: "Ok",
                                                customClass: {
                                                    confirmButton: "btn btn-primary"
                                                }
                                            });
                                        }
                                    });
                            }
                        });
                    }
                }

                // Handle submit button
                submitButton.addEventListener('click', e => {
                    e.preventDefault();
                    handleSubmitSave()
                })

                // Handle submit button
                submitAssignButton.addEventListener('click', e => {
                    e.preventDefault();
                    handleSubmitSave('save_and_assign')
                })
            }


            // Public methods
            return {
                init: function() {

                    initFlatpickr();
                    initQuill();

                    // Handle forms
                    handleStatus();
                    handleSubmit();
                }
            };
        }();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTAppSavebranch.init();
        });
    </script>


    <script>
        "use strict";

        var KTPos = (function() {
            var table;
            var datatable;
            var toolbarBase;
            var toolbarSelected;
            var selectedCount;


            // Private functions
            var initPosTable = function() {
                datatable = $(table).DataTable({
                    info: false,
                    paging: false,
                    pageLength: -1,
                    order: [],
                    columnDefs: [{
                        orderable: false,
                        targets: 0
                    }, ],
                    scrollX: false,
                    autoWidth: false,
                    language: {
                        emptyTable
                    },
                });

                datatable.on("draw", function() {
                    toggleToolbars();
                });
            };


            // Toggle toolbars
            const toggleToolbars = () => {
                // Select refreshed checkbox DOM elements
                const allCheckboxes = table.querySelectorAll('tbody [type="checkbox"]');

                // Detect checkboxes state & count
                let checkedState = false;
                let count = 0;

                // Count checked boxes
                allCheckboxes.forEach((c) => {
                    if (c.checked) {
                        checkedState = true;
                        count++;
                    }
                });

                // Toggle toolbars
                if (checkedState) {
                    selectedCount.innerHTML = count;
                    toolbarBase.classList.add("d-none");
                    toolbarSelected.classList.remove("d-none");
                } else {
                    toolbarBase.classList.remove("d-none");
                    toolbarSelected.classList.add("d-none");
                }
            };

            return {
                // Public functions
                init: function() {
                    table = document.querySelector('#kt_table_province');
                    if (!table) {
                        return;
                    }
                    initPosTable();
                },
            };
        })();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTPos.init();
        });
    </script>

    <script>
        $(document).ready(function() {

            function convertQueryStringToObject(queryString) {
                var query = {};
                var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
                for (var i = 0; i < pairs.length; i++) {
                    var pair = pairs[i].split('=');
                    query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
                }
                return query;
            }

            $(document).on('click', '#kt_modal_assign_province .pagination a, #kt_modal_assign_province .reset',
                function(e) {
                    e.preventDefault();
                    var url = $(this).attr('href');
                    loadModalContent(url);
                });

            $(document).on('change', '#kt_modal_assign_province select[name="kt_pagination_table_pagesize"]',
                function(e) {
                    var newUrl = $(this).val();
                    loadModalContent(newUrl);
                });

            // Submit form bộ lọc
            $(document).on('submit', '#kt_modal_assign_province #filterForm', function(e) {
                e.preventDefault();

                var url = $(this).attr('action');
                var queryStringData = $(this).serialize();
                var data = convertQueryStringToObject(queryStringData);
                loadModalContent(url, data);
            });

            $(document).on('click', '#kt_modal_assign_province #assign-multiple-pos-by-filters', function(e) {
                var url = `{{ route('branches.getProvincesAssignContent', $branch->code) }}`;
                var queryStringData = $(this).closest('#filterForm').serialize();
                var data = convertQueryStringToObject(queryStringData);

                Swal.fire({
                    text: 'Bạn có chắc chắn muốn phân Tỉnh/Thành phố đã lọc cho chi nhánh "{{ $branch->name }}" này không?',
                    icon: "warning",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "Đồng ý ",
                    cancelButtonText: "{{ __('Cancel') }}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-danger",
                        cancelButton: "btn fw-bold btn-active-light-primary",
                    },
                }).then(function(result) {
                    if (result.value) {
                        if (result.value) {
                            axios
                                .post(url, data)
                                .then((response) => {
                                    const data = response.data;
                                    if (data.success) {
                                        KTApp.notify({
                                            title: 'Cập nhật',
                                            text: data.message ||
                                                "Survey has been successfully updated!"
                                        })
                                        window.location.reload();

                                    } else {
                                        KTApp.notify({
                                            icon: 'error',
                                            title: 'Cập nhật',
                                            text: data.message ||
                                                "An error occurred. Please try again."
                                        })
                                    }
                                })
                                .catch((error) => {
                                    console.error(
                                        "Lỗi trong quá trình gửi yêu cầu:",
                                        error
                                    );
                                });

                            const headerCheckbox =
                                table.querySelectorAll('[type="checkbox"]')[0];
                            headerCheckbox.checked = false;
                        }

                    }
                });
            });


            function loadModalContent(url, params = {}) {
                console.log('Loading content...');
                console.log(url)
                axios.get(url, {
                        params: params
                    })
                    .then(function(response) {
                        $('#kt_modal_assign_province .modal-body').html(response.data.html);
                        KTMenu.createInstances();
                        $('[name="branch"]').select2({});
                        $('[name="province"]').select2({});
                    })
                    .catch(function(error) {
                        console.error('Error loading content:', error);
                    });
            }

            loadModalContent("{{ route('branches.getProvincesAssignContent', $branch->code) }}");

        });
    </script>


    <script>
        $(document).ready(function() {
            $('.edit-icon').click(function() {
                $(this).siblings('.min-visit-input').prop('disabled', false).focus();
                $(this).addClass('d-none');
                $(this).siblings('.save-icon').removeClass('d-none');
            });

            $('.save-icon').click(function() {
                var input = $(this).siblings('.min-visit-input');
                var provinceCode = input.data('province-code');
                var branchCode = input.data('branch-code');
                var minVisit = input.val();

                $.ajax({
                    url: '{{ route('branches.updateMinVisit') }}', // Update with your URL
                    type: 'POST',
                    data: {
                        'min_visit': minVisit,
                        'province_code': provinceCode,
                        'branch_code': branchCode,
                        '_token': '{{ csrf_token() }}'
                    },
                    success: function({
                        message
                    }) {
                        KTApp.notify({
                            title: message
                        })
                        // On success
                        input.prop('disabled', true);
                        $('.edit-icon').removeClass('d-none');
                        $('.save-icon').addClass('d-none');
                    },
                    error: function(error) {
                        // Handle error
                        console.error(error);
                    }
                });
            });

            // Optional: Handle outside click to cancel edit
            // $(document).click(function(event) {
            //     if (!$(event.target).closest('.input-group').length) {
            //         $('.min-visit-input').prop('disabled', true);
            //         $('.edit-icon').removeClass('d-none');
            //         $('.save-icon').addClass('d-none');
            //     }
            // });
        });
    </script>


@stop
