<div class="card">
    <!--begin::Card header-->
    <form action="{{route('branches.getProvincesAssignContent',$branch->code)}}" method="GET" id="filterForm">
        <div class="card-header border-0 pt-6">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center provinceition-relative my-1">
                    <i class="ki-duotone ki-magnifier fs-3 provinceition-absolute ms-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <input type="text" name="search" value="{{@$filters['search']}}"
                           class="form-control w-250px ps-13 w-ms-100"
                           placeholder="{{ __('Search province') }}"/>
                </div>

                <!--end::Search-->
            </div>
            <!--begin::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar" data-kt-province-table-modal-toolbar="base">
                <!--end::Toolbar-->
                <!--begin::Group actions-->
             {{--   @if( count($provinces)>0 && !empty(array_filter($filters)) )
                    <button type="button"
                            class="btn btn-info"
                            id="assign-multiple-province-by-filters"
                    >Phân giao tất cả ({{count($provinces)}})

                    </button>
                @endif--}}

                <div class="d-flex justify-content-end align-items-center d-none"
                     data-kt-province-table-modal-toolbar="selected">

                    <div class="fw-bold me-5">
                            <span class="me-2"
                                  data-kt-province-table-modal-select="selected_count"></span>{{ __('Selected') }}
                    </div>
                    <button type="button" class="btn btn-info"
                            data-assign-multiple-modal="{{ route('branches.assignProvincesToBranch',$branch->code) }}"
                            id="assign-multiple-province-modal"
                            data-kt-province-table-modal-select="assign_selected">Phân giao
                    </button>


                </div>
                <!--end::Group actions-->
            </div>
            <!--end::Card toolbar-->
        </div>
    </form>
    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body py-4 table-content">
        <!--begin::Table-->
        <table class="table align-middle table-row-dashed fs-6 gy-5"
               id="kt_model_table_province">
            <thead>
            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                <th class="w-10px pe-2">
                    <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                        <input class="form-check-input" type="checkbox" data-kt-check="true"
                               data-kt-check-target="#kt_model_table_province .form-check-input" value=""/>
                    </div>
                </th>
                <th>Tỉnh/Thành phố</th>
                <th>CODE</th>
                <th>Chi nhánh đã phân giao</th>
{{--                <th>Tổng số điểm bán</th>--}}
            </tr>
            </thead>
            <tbody class="text-gray-600 fw-semibold" id="kt_tbody_province">

            @foreach( $provinces as $province)
                <tr>
                    <td>
                        <div class="form-check form-check-sm form-check-custom form-check-solid">
                            <input name="selected_items[]" class="form-check-input" type="checkbox"
                                   value="{{ $province->locationCode }}"/>
                        </div>
                    </td>
                    <td>
                        {{ $province->locationName }}
                    </td>

                    <td>{{ $province->locationCode }}</td>

                    <td>
                        @forelse ($province->branches as $branch)
                            <!--begin::Badges-->
                            <div class="badge badge-light-success">{{ $branch->name }} </div>
                            <!--end::Badges-->
                        @empty

                        @endforelse
                    </td>

                </tr>
            @endforeach

            </tbody>
        </table>
        <!--end::Table-->
    </div>
    <!--end::Card body-->
</div>


<script>
    var KTModelProvince = (function () {
        var table = document.getElementById("kt_model_table_province");
        var datatable;
        var toolbarBase;
        var toolbarSelected;
        var selectedCount;

        // Private functions
        var initprovinceTable = function () {

            datatable = $(table).DataTable({
                info: false,
                order: [],
                lengthChange: false,
                language: {
                    emptyTable
                },
                columnDefs: [
                    {orderable: false, targets: 0}, // Disable ordering on column 0 (checkbox)
                ],
                paging: false,
                pageLength: -1,
            });

            // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
            datatable.on("draw", function () {
                initToggleToolbar();
                toggleToolbars();
            });
        };

        // Delete subscirption


        // Init toggle toolbar
        var initToggleToolbar = () => {
            const checkboxes = table.querySelectorAll('[type="checkbox"]');

            // Select elements
            toolbarBase = document.querySelector(
                '[data-kt-province-table-modal-toolbar="base"]'
            );
            toolbarSelected = document.querySelector(
                '[data-kt-province-table-modal-toolbar="selected"]'
            );
            selectedCount = document.querySelector(
                '[data-kt-province-table-modal-select="selected_count"]'
            );
            const assignSelected = document.querySelector(
                '[data-kt-province-table-modal-select="assign_selected"]'
            );

            const assignMultipleUrl = document
                .getElementById("assign-multiple-province-modal")
                .getAttribute("data-assign-multiple-modal");


            checkboxes.forEach((c) => {
                c.addEventListener("click", function () {
                    setTimeout(function () {
                        toggleToolbars();
                    }, 50);
                });
            });

            // assign selected rows
            assignSelected.addEventListener("click", function () {

                Swal.fire({
                    text: 'Bạn có chắc chắn muốn phân giao Tỉnh/Thành phố đã chọn cho chi nhánh {{$branch->name}} này không?',
                    icon: "warning",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "Đồng ý ",
                    cancelButtonText: "{{__('Cancel')}}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-danger",
                        cancelButton: "btn fw-bold btn-active-light-primary",
                    },
                }).then(function (result) {
                    if (result.value) {
                        const checkedValues = [];
                        checkboxes.forEach((c) => {
                            if (c.checked && c.value) {
                                checkedValues.push(c.value);
                            }
                        });
                        axios
                            .post(assignMultipleUrl, {
                                province_ids: checkedValues,
                                _token: KTApp.csrfToken(),
                            })
                            .then((response) => {
                                const data = response.data;
                                if (data.success) {
                                    KTApp.notify({
                                        title: 'Cập nhật',
                                        text: data.message || "Survey has been successfully updated!"
                                    })
                                    window.location.reload();

                                } else {
                                    KTApp.notify({
                                        icon: 'error',
                                        title: 'Cập nhật',
                                        text: data.message || "An error occurred. Please try again."
                                    })
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    "Lỗi trong quá trình gửi yêu cầu:",
                                    error
                                );
                            });

                        const headerCheckbox =
                            table.querySelectorAll('[type="checkbox"]')[0];
                        headerCheckbox.checked = false;
                    }
                });
            });


        };

        // Toggle toolbars
        const toggleToolbars = () => {
            // Select refreshed checkbox DOM elements
            const allCheckboxes = table.querySelectorAll('tbody [type="checkbox"]');
            const assignAllBtn = document.getElementById('assign-multiple-province-by-filters')

            // Detect checkboxes state & count
            let checkedState = false;
            let count = 0;

            // Count checked boxes
            allCheckboxes.forEach((c) => {
                if (c.checked) {
                    checkedState = true;
                    count++;
                }
            });

            // Toggle toolbars
            if (checkedState) {
                selectedCount.innerHTML = count;
                toolbarBase.classList.remove("d-none");
                assignAllBtn?.classList.remove("d-none");
                toolbarSelected.classList.remove("d-none");
            } else {
                assignAllBtn?.classList.add("d-none");
                toolbarBase.classList.add("d-none");
                toolbarSelected.classList.add("d-none");
            }
        };

        return {
            // Public functions
            init: function () {
                table = document.getElementById("kt_model_table_province");

                if (!table) {
                    return;
                }
                initprovinceTable();
                initToggleToolbar();
            },
        };
    })();
    // On document ready
    KTUtil.onDOMContentLoaded(function () {
        KTModelProvince.init();
    });

</script>

