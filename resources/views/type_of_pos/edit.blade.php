@extends('layouts.app')

@section('page-title', __('Update'))
@section('page-heading', __('Update'))

@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        T<PERSON><PERSON><PERSON> lập
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">@lang('Pos')</li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        <a href="{{ route('type-of-pos.index') }}">{{__('Type of pos')}}
    </li></a>

    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>

    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">@yield('page-title')</li>
@stop


@section('style')
@endsection

@section('content')

    <div id="kt_app_content" class="app-content">

        <form id="form-type-of-pos"
            action="{{ route('type-of-pos.update', $evaluation_criteria) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="d-flex flex-column gap-7 gap-lg-10">
                <div class="card card-flush py-4">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>@lang('Update')</h2>
                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <!--begin::Input group-->
                        <div class="mb-10 fv-row fv-plugins-icon-container fv-plugins-bootstrap5-row-invalid">
                            <!--begin::Label-->
                            <label class="required form-label">@lang('Name')</label>
                            <!--end::Label-->
                            <!--begin::Input-->
                            <input type="text" name="name" class="form-control mb-2"
                                placeholder="@lang('Name')..." value="{{ $evaluation_criteria->name }}">
                            <!--end::Input-->

                        </div>
                    </div>


                </div>

                <div class="d-flex justify-content-end">
                    <!--begin::Button-->
                    <a href="{{ route('type-of-pos.index') }}" id="kt_ecommerce_add_product_cancel"
                        class="btn btn-light me-5">@lang('Cancel')</a>
                    <!--end::Button-->
                    <!--begin::Button-->
                    <button type="submit" id="submitButton" class="btn btn-primary">
                        <span class="indicator-label">@lang('Update')</span>
                        <span class="indicator-progress">@lang('Please wait')...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                    </button>
                    <!--end::Button-->
                </div>
            </div>
        </form>
    </div>

@stop

@section('script-vendor')

@stop

@push('script-component')
    <script>
        "use strict";

        // Class definition
        var KTAppUnit = (function() {
            // Get elements
            const form = document.getElementById("form-type-of-pos");
            const submitButton = document.getElementById("submitButton");

            // Submit formCreate handler
            const handleSubmit = () => {
                // Define variables
                let validator;

                // Init formCreate validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
                validator = FormValidation.formValidation(form, {
                    fields: {
                        name: {
                            validators: {
                                notEmpty: {
                                    message: "Tên là bắt buộc",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });

                // Handle submit button
                submitButton.addEventListener("click", (e) => {
                    e.preventDefault();

                    // Validate formCreate before submit
                    if (validator) {
                        validator.validate().then(function(status) {

                            if (status == "Valid") {
                                // Show loading indication
                                submitButton.setAttribute("data-kt-indicator", "on");

                                // Disable button to avoid multiple click
                                submitButton.disabled = true;
                                form.submit();
                            }
                        });
                    }
                });
            };
            // Public methods
            return {
                init: function() {
                    handleSubmit();
                },
            };
        })();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTAppUnit.init();
        });
    </script>
@endpush
