@extends('layouts.auth')

@section('page-title', 'Yêu cầu xóa dữ liệu')

@section('content')

    <div class="d-flex flex-column flex-lg-row flex-column-fluid">
        <!--begin::Body-->
        <div class="d-flex flex-column flex-lg-row-fluid w-lg-50 p-10 order-2 order-lg-1">
            <!--begin::Form-->
            <div class="d-flex flex-center flex-column flex-lg-row-fluid">
                <!--begin::Wrapper-->
                <div class="w-lg-500px p-10">
                    <!--begin::Form-->
                    <form class="form w-100" id="delete_account_form" method="POST" action="{{ route('account.delete') }}">
                        @csrf
                        <!--begin::Heading-->
                        <div class="text-center mb-11">
                            <!--begin::Title-->
                            <h1 class="text-gray-900 fw-bolder mb-3">@yield('page-title') </h1>
                            <p><PERSON>úng tôi cam kết bảo vệ quyền riêng tư của bạn. N<PERSON>u bạn muốn xóa toàn bộ dữ liệu cá nhân của mình trên ứng dụng DMS Vietlott,
                                vui lòng điền thông tin dưới đây và gửi yêu cầu.</p>
                        </div>


                        <!--begin::Input group=-->
                        <div class="fv-row mb-8">
                            <!--begin::Email-->
                            <label class="form-label required" for="">Tài khoản đăng nhập</label>
                            <input type="text" placeholder="@lang('Username')" name="username"
                                value="{{ old('username') }}" autocomplete="off" class="form-control " />
                            <!--end::Email-->
                        </div>

{{--                        <div class="fv-row mb-8">--}}
{{--                            <!--begin::Password-->--}}
{{--                            <label class="form-label required" for="">Mật khẩu</label>--}}
{{--                            <input type="password" placeholder="@lang('Password')" name="password" autocomplete="off"--}}
{{--                                class="form-control bg-transparent" />--}}
{{--                            <!--end::Password-->--}}
{{--                        </div>--}}

                        <!--end::Input group=-->
                        <div class="fv-row mb-8">
                            <!--begin::Password-->
                            <label class="form-label required" for="">Lý do xóa dữ liệu</label>
                            <textarea type="password" placeholder="Vui lòng cung cấp lý do xóa dữ liệu" name="reason" autocomplete="off" data-kt-autosize="true"
                                      rows="1" class="form-control ">{!! old('reason') !!}</textarea>
                            <!--end::Password-->
                        </div>
                        <!--end::Wrapper-->
                        @if (setting('registration.captcha.enabled'))

                            <div class="fv-row mb-3">
                                    {!! app('captcha')->display() !!}

                            </div>

                        @endif
                        <!--begin::Submit button-->
                        <div class="d-grid mb-10 mt-10">
                            <button type="submit" id="submit-btn" class="btn btn-primary">
                                <!--begin::Indicator label-->
                                <span class="indicator-label"> Gửi yêu cầu</span>
                                <!--end::Indicator label-->
                                <!--begin::Indicator progress-->
                                <span class="indicator-progress">{{ __('Please wait') }}...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                <!--end::Indicator progress-->
                            </button>
                        </div>
                        <!--end::Submit button-->

                    </form>
                    <!--end::Form-->
                </div>
                <!--end::Wrapper-->
            </div>

        </div>
        <!--end::Body-->
        <!--begin::Aside-->
        @include('auth.partials.aside')
        <!--end::Aside-->
    </div>

@stop


@section('scripts')
    @if (setting('registration.captcha.enabled'))
        {!! HTML::script('https://www.google.com/recaptcha/api.js') !!}
    @endif
    <script>
        "use strict";

        // Class definition
        var KTSigninGeneral = function() {
            // Elements
            var form;
            var submitButton;
            var validator;

            // Handle form
            var handleValidation = function(e) {
                // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
                validator = FormValidation.formValidation(
                    form, {
                        fields: {

                            'password': {
                                validators: {
                                    notEmpty: {
                                        message: 'Mật khẩu là bắt buộc'
                                    }
                                }
                            },

                            'username': {
                                validators: {
                                    notEmpty: {
                                        message: 'Tên người dùng là bắt buộc'
                                    }
                                }
                            },
                            'reason': {
                                validators: {
                                    notEmpty: {
                                        message: 'Lý do là bắt buộc'
                                    }
                                }
                            }
                        },
                        plugins: {
                            trigger: new FormValidation.plugins.Trigger(),
                            bootstrap: new FormValidation.plugins.Bootstrap5({
                                rowSelector: '.fv-row',
                                eleInvalidClass: '', // comment to enable invalid state icons
                                eleValidClass: '' // comment to enable valid state icons
                            })
                        }
                    }
                );
            }

            var handleSubmitAjax = function(e) {

                // Handle form submit
                submitButton.addEventListener('click', function(e) {
                    // Prevent button default action
                    e.preventDefault();

                    // Validate form
                    validator.validate().then(function(status) {
                        if (status === 'Valid') {
                            submitButton.setAttribute('data-kt-indicator', 'on');

                            submitButton.disabled = true;

                            form.submit();
                        }
                    });
                });
            }

            var isValidUrl = function(url) {
                try {
                    new URL(url);
                    return true;
                } catch (e) {
                    return false;
                }
            }

            // Public functions
            return {
                // Initialization
                init: function() {
                    form = document.querySelector('#delete_account_form');
                    submitButton = document.querySelector('#submit-btn');
                    handleValidation();

                    if (isValidUrl(submitButton.closest('form').getAttribute('action'))) {

                        handleSubmitAjax(); // use for ajax submit
                    }
                }
            };
        }();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTSigninGeneral.init();
        });
    </script>
@endsection
