<!--begin::Modal - Add task-->
<div class="modal fade" id="kt_modal_add_pos" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <!--begin::Modal content-->
        <div class="modal-content">
            <!--begin::Modal header-->
            <div class="modal-header" id="kt_modal_add_user_header">
                <!--begin::Modal title-->
                <h2 class="fw-bold">{{ __('Add Pos') }}</h2>
                <!--end::Modal title-->
                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-icon-primary"
                     data-kt-users-modal-action="close">
                    <i class="ki-duotone ki-cross fs-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                </div>
                <!--end::Close-->
            </div>
            <!--end::Modal header-->
            <!--begin::Modal body-->
            <div class="modal-body px-5 my-7">
                <!--begin::Form-->
                <form id="kt_modal_add_pos_form" class="form" action="{{ route('users.store') }}"
                      method="POST">
                    @csrf
                    <!--begin::Scroll-->
                    <div class="d-flex flex-column scroll-y px-5 px-lg-10"
                         id="kt_modal_add_pos_scroll" data-kt-scroll="true"
                         data-kt-scroll-activate="true" data-kt-scroll-max-height="auto"
                         data-kt-scroll-dependencies="#kt_modal_add_pos_header"
                         data-kt-scroll-wrappers="#kt_modal_add_pos_scroll"
                         data-kt-scroll-offset="300px">
                        <!--begin::Input group-->
                        {{-- <div class="fv-row mb-7">
                            <!--begin::Label-->
                            <label class="d-block fw-semibold fs-6 mb-5">{{ __('Avatar') }}</label>
                            <!--end::Label-->
                            <!--begin::Image placeholder-->
                            <style>
                                .image-input-placeholder {
                                    background-image: url('assets/media/svg/files/blank-image.svg');
                                }

                                [data-bs-theme="dark"] .image-input-placeholder {
                                    background-image: url('assets/media/svg/files/blank-image-dark.svg');
                                }
                            </style>
                            <!--end::Image placeholder-->
                            <!--begin::Image input-->
                            <div class="image-input image-input-outline image-input-placeholder"
                                data-kt-image-input="true">
                                <!--begin::Preview existing avatar-->
                                <div class="image-input-wrapper w-125px h-125px"
                                    style="background-image: url(assets/media/avatars/300-6.jpg);">
                                </div>
                                <!--end::Preview existing avatar-->
                                <!--begin::Label-->
                                <label
                                    class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                    title="Change avatar">
                                    <i class="ki-duotone ki-pencil fs-7">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    <!--begin::Inputs-->
                                    <input type="file" name="avatar"
                                        accept=".png, .jpg, .jpeg" />
                                    <input type="hidden" name="avatar_remove" />
                                    <!--end::Inputs-->
                                </label>
                                <!--end::Label-->
                                <!--begin::Cancel-->
                                <span
                                    class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                    title="Cancel avatar">
                                    <i class="ki-duotone ki-cross fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <!--end::Cancel-->
                                <!--begin::Remove-->
                                <span
                                    class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                    title="Remove avatar">
                                    <i class="ki-duotone ki-cross fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <!--end::Remove-->
                            </div>
                            <!--end::Image input-->
                            <!--begin::Hint-->
                            <div class="form-text">{{ __('Allowed file types') }}: png, jpg, jpeg.
                            </div>
                            <!--end::Hint-->
                        </div> --}}
                        <!--end::Input group-->
                        <!--begin::Input group-->

                        <div class="row g-9 mb-7">
                            <div class="col-md-6 fv-row">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold mb-2 ">{{ __('Pos Name') }}</label>

                                {{--                                                    <select id="kt_add_posName" name="pos_name" aria-label="Fill Pos Name"--}}
                                {{--                                                        data-control="select2" data-placeholder="Fill Pos Name..."--}}
                                {{--                                                        class="form-select "--}}
                                {{--                                                        data-dropdown-parent="#kt_modal_add_pos">--}}
                                {{--                                                        <option></option>--}}
                                <input id="kt_add_posName" type="text" class="form-control "
                                       placeholder="Fill Pos Name..." name="posname" value="" />
                                {{--                                                        @foreach ($roles as $role)--}}
                                {{--                                                        <option value="{{$role->id}}">{{$role->display_name ?? $role->name}}</option>--}}
                                {{--                                                        @endforeach--}}
                                {{--                                                    </select>--}}
                            </div>
                            <div class="col-md-6 fv-row">
                                <!--begin::Label-->
                                {{-- @dd($statuses) --}}
                                <label class="fs-6 fw-semibold mb-2">{{ __('Pos Code') }}</label>
                                <input id="kt_add_posCode" type="text" class="form-control "
                                       placeholder="Fill Pos code..." name="poscode" value="" />

                                {{--                                                    <select id="kt_add_userStatus" name="status" aria-label="Fill Pos Cpde"--}}
                                {{--                                                        data-control="select2" data-placeholder="Chọn một vai trò..."--}}
                                {{--                                                        class="form-select "--}}
                                {{--                                                        data-dropdown-parent="#kt_modal_add_pos">--}}

                                {{--                                                        @foreach ($statuses as $status)--}}
                                {{--                                                        <option value="{{@$statuses[$status]}}">{{__($status)}}</option>--}}
                                {{--                                                        @endforeach--}}
                                {{--                                                    </select>--}}
                            </div>
                        </div>
                        {{--                                            loại hình ĐBH--}}
                        <div  class="row g-9 mb-7">
                            <div class="col-md-6 fv-row">
                                <label class="fs-6 fw-semibold mb-2 ">{{ __('Pos Type') }}</label>
                                <select id="kt_add_posName" name="pos_name" aria-label="Select Pos Type"
                                        data-control="select2" data-placeholder="Select Pos Type..."
                                        class="form-select "
                                        data-dropdown-parent="#kt_modal_add_pos">
                                </select>
                            </div>
                            <div class="col-md-6 fv-row">
                                <label class="fs-6 fw-semibold mb-2">{{ __('Agency') }}</label>
                                <select id="kt_add_Agency" name="agency" aria-label="Select a Agency"
                                        data-control="select2" data-placeholder="Select a Agency..."
                                        class="form-select "
                                        data-dropdown-parent="#kt_modal_add_pos">
                                </select>
                            </div>
                        </div>
                        <div class="fv-row mb-7">
                            <label class="fs-6 fw-semibold mb-2">{{ __('Area') }}</label>
                            <select id="kt_add_area" name="area" aria-label="Select a Area"
                                    data-control="select2" data-placeholder="Select a Area..."
                                    class="form-select "
                                    data-dropdown-parent="#kt_modal_add_area">
                            </select>
                        </div>
                        <div class="fv-row mb-7">
                            <label class="fs-6 fw-semibold mb-2">{{ __('Province/City') }}</label>
                            <select id="kt_add_area" name="area" aria-label="Select a Area"
                                    data-control="select2" data-placeholder="Select a Area..."
                                    class="form-select "
                                    data-dropdown-parent="#kt_modal_add_area">
                            </select>
                        </div>
                        <div class="fv-row mb-7">
                            <label class="fs-6 fw-semibold mb-2">{{ __('District') }}</label>
                            <select id="kt_add_area" name="area" aria-label="Select a Area"
                                    data-control="select2" data-placeholder="Select a Area..."
                                    class="form-select "
                                    data-dropdown-parent="#kt_modal_add_area">
                            </select>
                        </div>
                        <div class="fv-row mb-7">
                            <label class="fs-6 fw-semibold mb-2">{{ __('Commune/Ward/Town') }}</label>
                            <select id="kt_add_area" name="area" aria-label="Select a Area"
                                    data-control="select2" data-placeholder="Select a Area..."
                                    class="form-select "
                                    data-dropdown-parent="#kt_modal_add_area">
                            </select>
                        </div>
                        {{--
                                                                    </div>
                        {{--                                            chọn địa chỉ--}}
                        {{--                                            <div class="fv-row mb-7">--}}
                        {{--                                                <!--begin::Label-->--}}
                        {{--                                                <label class="fs-6 fw-semibold mb-2">--}}
                        {{--                                                    <span class="required" >Email</span>--}}
                        {{--                                                    <span class="ms-1" data-bs-toggle="tooltip"--}}
                        {{--                                                        title="{{ __('Địa chỉ email phải đang hoạt động') }} ">--}}
                        {{--                                                        <i class="ki-duotone ki-information fs-7">--}}
                        {{--                                                            <span class="path1"></span>--}}
                        {{--                                                            <span class="path2"></span>--}}
                        {{--                                                            <span class="path3"></span>--}}
                        {{--                                                        </i>--}}
                        {{--                                                    </span>--}}
                        {{--                                                </label>--}}
                        {{--                                                <!--end::Label-->--}}
                        {{--                                                <!--begin::Input-->--}}
                        {{--                                                <input type="email" id="kt_add_userEmail" class="form-control "--}}
                        {{--                                                    placeholder="" name="email" value="" />--}}
                        {{--                                                <!--end::Input-->--}}
                        {{--                                            </div>--}}
                        {{--                                            nhập địa chỉ chi tiết--}}
                        <div class="fv-row mb-7">
                            <!--begin::Label-->
                            <label class="fs-6 fw-semibold mb-2">{{ __('Address Detail') }} </label>
                            <!--end::Label-->
                            <!--begin::Input-->
                            <input id="kt_add_posAddress" class="form-control "
                                   placeholder="Fill..."
                                   name="address" />
                            <!--end::Input-->
                        </div>
                        <div class="fv-row mb-7">
                            <!--begin::Label-->
                            <label class="fs-6 fw-semibold mb-2">{{ __('Pos Status') }} </label>
                            <!--end::Label-->
                            <!--begin::Input-->
                            <select id="kt_add_posStatus" name="posStatus" aria-label="Select a Status"
                                    data-control="select2" data-placeholder="Select a Status..."
                                    class="form-select "
                                    data-dropdown-parent="#kt_modal_add_status">
                            </select>
                            <!--end::Input-->
                        </div>
                        <div class="row g-9 mb-7">
                            <div class="col-md-6 fv-row">
                                <label class="fs-6 fw-semibold mb-2 ">{{ __('Pos certificate number') }}</label>
                                <input id="kt_add_gcnPos" class="form-control "
                                       placeholder="Fill..."
                                       name="gcn" />
                            </div>
                            <div class="col-md-6 fv-row">
                                <label class="fs-6 fw-semibold mb-2">{{ __('Relationships agency') }}</label>
                                <input id="kt_add_relationshipsAgengy" class="form-control "
                                       placeholder="Fill..."
                                       name="relationshipsAgency" />
                            </div>
                        </div>
                        <div class="row g-9 mb-7">
                            <div class="col-md-6 fv-row">
                                <label class="fs-6 fw-semibold mb-2 ">{{ __('Contract addendum') }}</label>
                                <input id="kt_add_contractAddendum" class="form-control "
                                       placeholder="Fill..."
                                       name="contractAddendum" />
                            </div>
                            <div class="col-md-6 fv-row">
                                <label class="fs-6 fw-semibold mb-2">{{ __('Sign day') }}</label>
                                <input id="kt_add_signDay" type="date" class="form-control "
                                       placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"
                                       name="signDay" />
                            </div>
                        </div>
                        <div class="fv-row mb-7">

                            <label class="fs-6 fw-semibold mb-2 ">{{ __('Number of additional/replacement contract annexes') }}</label>
                            <input id="kt_add_contractAddendum" class="form-control "
                                   placeholder="Fill..."
                                   name="contractAddendum" />
                            {{--                                                <div class="col-md-6 fv-row">--}}
                            {{--                                                    <label class="fs-6 fw-semibold mb-2">{{ __('Sign Day') }}</label>--}}
                            {{--                                                    <input id="kt_add_signDay" type="date" class="form-control "--}}
                            {{--                                                           placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"--}}
                            {{--                                                           name="birthday" />--}}
                            {{--                                                </div>--}}
                        </div>
                        <div class="fv-row mb-7">

                            <label class="fs-6 fw-semibold mb-2 ">{{ __('Date of signing additional/replacement contract') }}</label>
                            <input id="kt_add_signDay" type="date" class="form-control "
                                   placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"
                                   name="signDay" />
                            {{--                                                <div class="col-md-6 fv-row">--}}
                            {{--                                                    <label class="fs-6 fw-semibold mb-2">{{ __('Sign Day') }}</label>--}}
                            {{--                                                    <input id="kt_add_signDay" type="date" class="form-control "--}}
                            {{--                                                           placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"--}}
                            {{--                                                           name="birthday" />--}}
                            {{--                                                </div>--}}
                        </div>
                        <div class="fv-row mb-7">

                            <label class="fs-6 fw-semibold mb-2 ">{{ __('Termination/replacement contract addendum number') }}</label>
                            <input id="kt_add_contractAddendum" class="form-control "
                                   placeholder="Fill..."
                                   name="contractAddendum" />
                            {{--                                                <div class="col-md-6 fv-row">--}}
                            {{--                                                    <label class="fs-6 fw-semibold mb-2">{{ __('Sign Day') }}</label>--}}
                            {{--                                                    <input id="kt_add_signDay" type="date" class="form-control "--}}
                            {{--                                                           placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"--}}
                            {{--                                                           name="birthday" />--}}
                            {{--                                                </div>--}}
                        </div>
                        <div class="fv-row mb-7">

                            <label class="fs-6 fw-semibold mb-2 ">{{ __('Date of signing the contract termination/replacement') }}</label>
                            <input id="kt_add_signDay" type="date" class="form-control "
                                   placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"
                                   name="signDay" />
                            {{--                                                <div class="col-md-6 fv-row">--}}
                            {{--                                                    <label class="fs-6 fw-semibold mb-2">{{ __('Sign Day') }}</label>--}}
                            {{--                                                    <input id="kt_add_signDay" type="date" class="form-control "--}}
                            {{--                                                           placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"--}}
                            {{--                                                           name="birthday" />--}}
                            {{--                                                </div>--}}
                        </div>
                        <div class="fv-row mb-7">

                            <label class="fs-6 fw-semibold mb-2 ">{{ __('Terminal') }}</label>
                            <input id="kt_add_signDay"  class="form-control "
                                   placeholder="Fill..."
                                   name="Terminal" />
                            {{--                                                <div class="col-md-6 fv-row">--}}
                            {{--                                                    <label class="fs-6 fw-semibold mb-2">{{ __('Sign Day') }}</label>--}}
                            {{--                                                    <input id="kt_add_signDay" type="date" class="form-control "--}}
                            {{--                                                           placeholder="dd/mm/yyyy" name="birthday" pattern="\d{1,2}/\d{1,2}/\d{4}"--}}
                            {{--                                                           name="birthday" />--}}
                            {{--                                                </div>--}}
                        </div>
                        <!--end::Input group-->
                        <!--begin::Input group-->
                        {{-- <div class="mb-5">
                            <!--begin::Label-->
                            <label
                                class="required fw-semibold fs-6 mb-5">{{ __('Role') }}</label>
                            <!--end::Label-->
                            <!--begin::Roles-->
                            <!--begin::Input row-->
                            <div class="d-flex fv-row">
                                <!--begin::Radio-->
                                <div class="form-check form-check-custom form-check-solid">
                                    <!--begin::Input-->
                                    <input class="form-check-input me-3" name="user_role"
                                        type="radio" value="0"
                                        id="kt_modal_update_role_option_0" checked='checked' />
                                    <!--end::Input-->
                                    <!--begin::Label-->
                                    <label class="form-check-label"
                                        for="kt_modal_update_role_option_0">
                                        <div class="fw-bold text-gray-800">{{ __('Administrator') }}
                                        </div>
                                        <div class="text-gray-600">
                                            {{ __('Best for business owners and company administrators') }}
                                        </div>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <!--end::Radio-->
                            </div>
                            <!--end::Input row-->
                            <div class='separator separator-dashed my-5'></div>
                            <!--begin::Input row-->
                            <div class="d-flex fv-row">
                                <!--begin::Radio-->
                                <div class="form-check form-check-custom form-check-solid">
                                    <!--begin::Input-->
                                    <input class="form-check-input me-3" name="user_role"
                                        type="radio" value="1"
                                        id="kt_modal_update_role_option_1" />
                                    <!--end::Input-->
                                    <!--begin::Label-->
                                    <label class="form-check-label"
                                        for="kt_modal_update_role_option_1">
                                        <div class="fw-bold text-gray-800">{{ __('Developer') }}
                                        </div>
                                        <div class="text-gray-600">
                                            {{ __('Best for developers or people primarily using the API') }}
                                        </div>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <!--end::Radio-->
                            </div>
                            <!--end::Input row-->
                            <div class='separator separator-dashed my-5'></div>
                            <!--begin::Input row-->
                            <div class="d-flex fv-row">
                                <!--begin::Radio-->
                                <div class="form-check form-check-custom form-check-solid">
                                    <!--begin::Input-->
                                    <input class="form-check-input me-3" name="user_role"
                                        type="radio" value="2"
                                        id="kt_modal_update_role_option_2" />
                                    <!--end::Input-->
                                    <!--begin::Label-->
                                    <label class="form-check-label"
                                        for="kt_modal_update_role_option_2">
                                        <div class="fw-bold text-gray-800">{{ __('Analyst') }}
                                        </div>
                                        <div class="text-gray-600">
                                            {{ __("Best for people who need full access to analytics data, but don't need to update business settings") }}
                                        </div>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <!--end::Radio-->
                            </div>
                            <!--end::Input row-->
                            <div class='separator separator-dashed my-5'></div>
                            <!--begin::Input row-->
                            <div class="d-flex fv-row">
                                <!--begin::Radio-->
                                <div class="form-check form-check-custom form-check-solid">
                                    <!--begin::Input-->
                                    <input class="form-check-input me-3" name="user_role"
                                        type="radio" value="3"
                                        id="kt_modal_update_role_option_3" />
                                    <!--end::Input-->
                                    <!--begin::Label-->
                                    <label class="form-check-label"
                                        for="kt_modal_update_role_option_3">
                                        <div class="fw-bold text-gray-800">{{ __('Support') }}
                                        </div>
                                        <div class="text-gray-600">
                                            {{ __('Best for employees who regularly refund payments and respond to disputes') }}
                                        </div>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <!--end::Radio-->
                            </div>
                            <!--end::Input row-->
                            <div class='separator separator-dashed my-5'></div>
                            <!--begin::Input row-->
                            <div class="d-flex fv-row">
                                <!--begin::Radio-->
                                <div class="form-check form-check-custom form-check-solid">
                                    <!--begin::Input-->
                                    <input class="form-check-input me-3" name="user_role"
                                        type="radio" value="4"
                                        id="kt_modal_update_role_option_4" />
                                    <!--end::Input-->
                                    <!--begin::Label-->
                                    <label class="form-check-label"
                                        for="kt_modal_update_role_option_4">
                                        <div class="fw-bold text-gray-800">{{ __('Trial') }}
                                        </div>
                                        <div class="text-gray-600">
                                            {{ __("Best for people who need to preview content data, but don't need to make any updates") }}
                                        </div>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <!--end::Radio-->
                            </div>
                            <!--end::Input row-->
                            <!--end::Roles-->
                        </div> --}}
                        <!--end::Input group-->
                    </div>
                    <!--end::Scroll-->
                    <!--begin::Actions-->

                    <input type="hidden" name="country_id" value="704">
                    {{-- <input type="hidden" name="role" value="{{Auth::user()->role_id}}" /> --}}

                    <div class="text-center pt-10">
                        <button type="reset" class="btn btn-light me-3"
                                data-kt-users-modal-action="cancel">{{ __('Discard') }}</button>
                        <button type="submit" class="btn btn-primary"
                                data-kt-users-modal-action="submit">
                            <span class="indicator-label">{{ __('Submit') }}</span>
                            <span class="indicator-progress">{{ __('Please wait') }}...
                                                    <span
                                                            class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                        </button>
                    </div>
                    <!--end::Actions-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Modal body-->
        </div>
        <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
</div>
<!--end::Modal - Add task-->
