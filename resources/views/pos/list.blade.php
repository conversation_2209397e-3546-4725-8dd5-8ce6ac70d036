@extends('layouts.app')

@section('page-title', __('Thiết lập điểm bán hàng'))
@section('page-heading', '')

@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Thiế<PERSON> lập
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Điểm bán
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">Danh sách @if ($approve)
            yêu cầu cập nhật tọa độ
        @endif
    </li>

@stop
@if (!$approve)
    @section('header-actions')
        <div class="card-toolbar">
            <!--begin::Toolbar-->
            <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
                <!--begin::Filter-->
                <div class="d-flex flex-wrap my-1">
                    <!--begin::Tab nav-->
                    <ul class="nav nav-pills me-5">
                        <li class="nav-item m-0">
                            <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary active me-3"
                                data-bs-toggle="tab" href="#kt_pos_table_pane">
                                <i class="ki-duotone ki-row-horizontal fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </a>
                        </li>
                        <li class="nav-item m-0">
                            <a class="btn btn-sm btn-icon btn-light btn-color-muted btn-active-primary" data-bs-toggle="tab"
                                href="#kt_pos_map_pane">
                                <i class="ki-duotone ki-map fs-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                            </a>
                        </li>
                    </ul>

                </div>
                <!--end::Content-->
            </div>
        </div>
    @stop
@endif

@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Thiết lập
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Điểm bán
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">Danh sách điểm bán</li>
@stop

@section('content')
    <div id="kt_app_content" class="app-content">
        <div class="card">
            @include('pos.partials.filter')
            <div class="card-body py-4 table-content tab-content">

                <div id="kt_pos_table_pane" class="tab-pane fade show active">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="pos-table">
                        <thead>
                            <tr class="text-start text-gray-800 fw-bold fs-7 text-uppercase gs-0">
                                @if ($approve)
                                    <th>
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" data-kt-check="true"
                                                data-kt-check-target="#pos-table .form-check-input" value="1">
                                        </div>
                                    </th>
                                @endif
                                <th>Mã ĐBH</th>
                                <th class="min-w-125px">Trạng thái</th>
                                <th class="min-w-125px">Y/C cập nhập</th>
                                <th class="min-w-125px">
                                    Ghé thăm
                                    <span data-bs-toggle="tooltip" data-bs-placement="top"
                                        title="Thời gian ghé thăm gần nhất">

                                        <i class="ki-duotone ki-information">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </span>
                                </th>
                                <th class="min-w-125px">
                                    DT {{ setting('pos_warn.no_revenue') }} ngày
                                    <span data-bs-toggle="tooltip" data-bs-placement="top"
                                        title="Doanh thu {{ setting('pos_warn.no_revenue') }} ngày gần đây">

                                        <i class="ki-duotone ki-information">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </span>
                                </th>
                                <th class="min-w-125px">Tỉnh/TP</th>
                                <th class="min-w-200px">Đại lý</th>
                                <th class="min-w-125px">CVKD</th>
                                <th class="min-w-200px">Địa chỉ</th>
                                {{-- <th class="mw-50px">TTB</th> --}}
                                <th class="text-end min-w-125px">{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-600 fw-semibold">
                            <tr class="text-center">
                                <td colspan="99">
                                    @include('partials.empty')
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div id="kt_pos_map_pane" class="tab-pane fade">
                    @include('partials.root-spa')
                </div>
            </div>
        </div>
    </div>
@stop

@section('style')
    {!! HTML::style(asset('assets/plugins/custom/datatables/datatables.bundle.css')) !!}
@endsection

@section('script-vendor')
    <script src="{{ asset('assets/plugins/custom/datatables/datatables.bundle.js') }}"></script>
    <script src="{{ asset('assets/js/custom/global/dropdown.js') }}"></script>
@stop

@section('script')
    <script>
        var pos_url = "{{ $approve ? route('pos.approve') : route('pos.list') }}";
        var checkboxes;
        const initDataTable = () => {
            var dataTablePos = $("#pos-table").DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: pos_url,
                    data: function(d) {
                        $.each($('#kt_ecommerce_add_pos_filter_form').serializeArray(), function() {
                            d[this.name] = this.value;
                        });
                    }
                },
                columns: [
                    @if ($approve)
                        {
                            data: null,
                            searchable: false,
                            orderable: false,
                            render: function(data, type, row) {
                                return '<div class="form-check form-check-sm form-check-custom form-check-solid">' +
                                    '<input class="form-check-input" type="checkbox" value="' + row
                                    .posCode + '">' +
                                    '</div>';
                            }
                        },
                    @endif {
                        data: "code",
                        name: "posCode"
                    },
                    {
                        data: "posStatus",
                        name: "posStatus",
                        searchable: false,
                    },
                    {
                        data: "posUpdate",
                        name: "posUpdate",
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: "last_checkin",
                        name: "last_checkin",
                        searchable: false,
                    },
                    {
                        data: "recent_revenue",
                        name: "recent_revenue",
                        searchable: false,
                    },
                    {
                        data: "locationName",
                        name: "locationName",
                        searchable: false,
                    },
                    {
                        data: "companyName",
                        name: "companyName",
                        searchable: false,
                        className: "fs-7",
                    },
                    {
                        data: "staff",
                        name: "staff",
                        searchable: false,
                    },
                    {
                        data: "address",
                        name: "address",
                        className: "fs-7",
                    },
                    // {
                    //     data: 'items',
                    //     searchable: false,
                    //     orderable: false,
                    //     render: items => items?.length || 0,
                    // },
                    {
                        data: "action",
                        name: "action",
                        className: "text-end",
                        orderable: false,
                        searchable: false,
                    },
                ],
                order: [
                    [1, "asc"]
                ],
                initComplete: function() {

                    const table = this.api();

                    const debouncedSearch = KTApp.datatableSearchInit(table);

                    $(document).find('input[name="search"]').on("keyup", function() {
                        debouncedSearch(this.value);
                    });
                },
                drawCallback: function() {
                    @if ($approve)
                        toggleToolbars();
                        checkboxes = document.querySelectorAll('.form-check-input');

                        checkboxes.forEach((c) => {
                            c.addEventListener("click", function() {
                                setTimeout(function() {
                                    toggleToolbars();
                                }, 50);
                            });
                        });
                    @endif
                },
            });

        }
        // $(document).ready(function() {
        //     initDataTable();
        // });

        var callCount = 0;

        var initDataTableWhenReady = () => {
            callCount++;
            if (callCount === 2) {
                initDataTable();
            }
        };

        var initdFilterProvince = () => {
            initDataTableWhenReady();
        };

        var initdFilterBranch = () => {
            initDataTableWhenReady();
        };
    </script>

    @if ($approve)
        <script>
            var checkedValues = [];
            const assignSelected = document.querySelector(
                '[data-kt-pos-table-modal-select="assign_selected"]'
            );

            const deselectSelected = document.querySelector(
                '[data-kt-pos-table-modal-select="deselect_selected"]'
            );

            const toolbarSelected = document.querySelector(
                '[data-kt-pos-table-modal-toolbar="selected"]'
            );
            const selectedCount = document.querySelector(
                '[data-kt-pos-table-modal-select="selected_count"]'
            );

            const toggleToolbars = () => {

                const allCheckboxes = document.querySelectorAll(
                    '#pos-table tbody .form-check-input');
                // Detect checkboxes state & count
                let checkedState = false;
                let count = 0;

                checkedValues = [];
                // Count checked boxes
                allCheckboxes.forEach((c) => {
                    if (c.checked) {
                        checkedState = true;
                        checkedValues.push(c.value);
                        count++;
                    }
                });

                // Toggle toolbars
                if (checkedState) {
                    selectedCount.innerHTML = count;
                    toolbarSelected.classList.remove("d-none");
                } else {
                    toolbarSelected.classList.add("d-none");
                }
            };


            assignSelected.addEventListener("click", function() {
                approvePos(checkedValues);
            });

            $(document).on('click', '.approve_update', function() {
                const posCode = $(this).data('pos-code');
                approvePos(posCode);
            });


            deselectSelected.addEventListener("click", function() {
                deselectPos(checkedValues);
            });


            $(document).on('click', '.reject_update', function() {
                const posCode = $(this).data('pos-code');
                deselectPos(posCode);
            });

            function approvePos(pos) {
                Swal.fire({
                    text: 'Bạn có chắc chắn muốn duyệt điểm bán hàng đã chọn không?',
                    icon: "info",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "Đồng ý ",
                    cancelButtonText: "{{ __('Cancel') }}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-info",
                        cancelButton: "btn fw-bold btn-active-light-primary",
                    },

                }).then(function(result) {
                    if (result.value) {

                        axios
                            .post('{{ route('pos.approveUpdate') }}', {
                                pos_ids: pos,
                                _token: KTApp.csrfToken(),
                            })
                            .then((response) => {
                                const data = response.data;
                                if (data.success) {
                                    KTApp.notify({
                                        title: data.message ||
                                            "Đã duyệt điểm bán thành công!",
                                    })

                                    $("#pos-table").DataTable().draw();

                                } else {
                                    KTApp.notify({
                                        icon: 'error',
                                        title: data.message ||
                                            "Lỗi trong quá trình gửi yêu cầu."
                                    })
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    "Lỗi trong quá trình gửi yêu cầu:",
                                    error
                                );
                            });
                    }
                });
            }

            function deselectPos(pos) {
                Swal.fire({
                    text: 'Bạn có chắc chắn không duyệt điểm bán hàng đã chọn không?',
                    icon: "warning",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "Đồng ý ",
                    cancelButtonText: "{{ __('Cancel') }}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-danger",
                        cancelButton: "btn fw-bold btn-active-light-primary",
                    },

                }).then(function(result) {
                    if (result.value) {
                        axios
                            .post('{{ route('pos.rejectUpdate') }}', {
                                pos_ids: pos,
                                _token: KTApp.csrfToken(),
                            })
                            .then((response) => {
                                const data = response.data;
                                if (data.success) {
                                    KTApp.notify({
                                        title: data.message ||
                                            "điểm bán đã không được duyệt!",
                                    })

                                    $("#pos-table").DataTable().draw();

                                } else {
                                    KTApp.notify({
                                        icon: 'error',
                                        title: data.message ||
                                            "Lỗi trong quá trình gửi yêu cầu."
                                    })
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    "Lỗi trong quá trình gửi yêu cầu:",
                                    error
                                );
                            });
                    }
                });
            }
        </script>
    @endif

    <script>
        $(document).ready(function() {
            $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
                var href = $(e.target).attr('href');
                history.pushState(null, null, href);
            });

            var activeTab = location.hash;
            if (activeTab) {
                $('.nav-pills a[href="' + activeTab + '"]').tab('show');
            }
        });
    </script>

    <script>
        const poses = @json($poses->resolve() ?? []);
        const lastPage = {{ $poses->lastPage() }};
        const currentPage = {{ $poses->currentPage() }};
        const nextPageUrl = "{{ $poses->nextPageUrl() }}";
        const pageSize = "{{ $poses->perPage() }}";

        const total = {{ $poses->total() }};
        const mode = {{ request()->mode ?? 3 }};
        const threshold_amount = {{ setting('pos_warn.threshold_amount') }};
        const warn_total_date_revenue = {{ setting('pos_warn.no_revenue') }};
        const googleMapsApiKey = "{{ env('GOOGLE_MAPS_API_KEY') }}";
    </script>

    @if (config('app.env') == 'local')
        @viteReactRefresh
    @endif

    @vite('resources/modules/map/index.jsx', 'build-report')
@stop
