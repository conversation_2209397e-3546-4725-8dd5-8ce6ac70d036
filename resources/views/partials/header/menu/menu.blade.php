<!--begin::Menu header-->
@foreach (config('menu') as $menu)
    @if (auth()->user()->hasPermission($menu['permission'], true, false))
        <div data-kt-menu-trigger="{default: 'click', xl: 'hover'}" data-kt-menu-placement="bottom-start"
            class="menu-item  {{ hasActiveSubMenu($menu) ? 'here show' : '' }} menu-here-bg menu-xl-down-accordion me-0 me-xl-2 fw-bold">
            <a target="{{ isset($menu['target']) ? $menu['target'] : '_self' }}" href="{{ checkRouteOrUrl($menu) }}"
                class="menu-link" id="{{ @$menu['id'] ?? '' }}"
                @isset($menu['tooltip_title'])
                    data-bs-toggle="tooltip"
                title="{{ $menu['tooltip_title'] }}"
                @endisset>
                <span class="menu-title menu-title-primary fw-bold">{{ @$menu['title'] }}</span>

                @if (auth()->user()->hasPermission(\App\Support\Enum\Perms::POS_MANAGER) &&
                        isset($ticketCount['support']) &&
                        $menu['id'] == 'communication' &&
                        $ticketCount['support'] > 0)
                    <span class="menu-badge"><span
                            class="badge badge-light-success">{{ $ticketCount['support'] }}</span></span>
                @endif

                @if (isset($menu['sub_menu']))
                    <span class="menu-arrow d-xl-none"></span>
                @endif

            </a>
            @if ($menu['styleMenu'] == 1)
                <div
                    class="menu-style-{{ $menu['styleMenu'] }} menu-sub menu-sub-xl-down-accordion menu-sub-xl-dropdown px-xl-2 py-xl-4 {{ @$menu['class_width'] ?? 'w-xl-260px' }}">
                    <div class="menu-item">
                        @foreach ($menu['sub_menu'] as $subMenu)
                            @if (
                                !isset($subMenu['permission']) ||
                                    auth()->user()->hasPermission($subMenu['permission'], true, false))
                                <a target="{{ isset($subMenu['target']) ? $subMenu['target'] : '_self' }}"
                                    href="{{ checkRouteOrUrl($subMenu) }}" id="{{ $subMenu['id'] }}"
                                    class="menu-link {{ str_replace('.', '-', $subMenu['class'] ?? '') }}  {{ hasActiveSubMenu(@$subMenu) ? 'active here show' : '' }} "
                                    @isset($subMenu['tooltip_title'])
                                        data-bs-toggle="tooltip"
                                    title="{{ $subMenu['tooltip_title'] }}"
                                    @endisset>
                                    <span class="menu-icon">
                                        @isset($subMenu['iconElement'])
                                            {!! $subMenu['iconElement'] !!}
                                        @else
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                        @endisset
                                    </span>
                                    <span class="menu-title">{{ $subMenu['title'] }}</span>
                                    @if ($subMenu['id'] == 'update_coordinates' && $countStatusPos > 0)
                                        <span class="menu-badge">
                                            <span class="badge badge-light-success">{{ $countStatusPos }}</span>
                                        </span>
                                    @endif
                                </a>
                            @endif
                        @endforeach
                    </div>
                </div>
            @endif
            @if ($menu['styleMenu'] == 2)
                <div
                    class="menu-style-{{ $menu['styleMenu'] }} menu-sub menu-sub-xl-down-accordion menu-sub-xl-dropdown p-0">
                    <div class="menu-active-bg px-4 px-xl-0">
                        <div class="tab-content py-4 py-xl-8 px-xl-7">
                            <div class="tab-pane active  {{ @$menu['class_width'] ?? ' w-xl-600px' }} ">
                                <div class="row">
                                    <div class="col-xl-12">
                                        <div class="row">
                                            @foreach ($menu['sub_menu'] as $subMenu)
                                                <div class="{{ @$menu['col'] ?? 'col-xl-6' }}  mb-6 mb-xl-0">
                                                    @isset($subMenu['title'])
                                                        <h4 class="fs-6 fs-xl-4 fw-bold mb-3 ms-4">
                                                            {{ $subMenu['title'] }}
                                                        </h4>
                                                    @endisset
                                                    <div class="mb-xl-6">
                                                        @foreach ($subMenu['sub_menu'] as $subMenu2)
                                                            @if (
                                                                !isset($subMenu2['permission']) ||
                                                                    auth()->user()->hasPermission($subMenu2['permission'], true, false))
                                                                <div
                                                                    class="menu-item p-0 m-0 {{ hasActiveSubMenu(@$subMenu2) ? 'here show' : '' }} ">

                                                                    <a target="{{ isset($subMenu2['target']) ? $subMenu2['target'] : '_self' }}"
                                                                        href="{{ checkRouteOrUrl($subMenu2) }}"
                                                                        id="{{ @$subMenu2['id'] }}"
                                                                        class="menu-link {{ str_replace('.', '-', $subMenu2['class'] ?? '') }}">

                                                                        <span class="menu-icon">

                                                                            @isset($subMenu2['iconElement'])
                                                                                {!! $subMenu2['iconElement'] !!}
                                                                            @else
                                                                                <span class="menu-bullet">
                                                                                    <span class="bullet bullet-dot"></span>
                                                                                </span>
                                                                            @endisset
                                                                        </span>
                                                                        <span
                                                                            class="menu-title">{{ $subMenu2['title'] }}</span>
                                                                    </a>
                                                                </div>
                                                            @endif
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            @if ($menu['styleMenu'] == 3)
                <!--begin:Menu sub-->
                <div
                    class="menu-style-{{ $menu['styleMenu'] }} menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown px-lg-2 py-lg-4 w-lg-250px">
                    <!--begin:Menu item-->
                    @foreach ($menu['sub_menu'] as $subMenu)
                        @if (
                            !isset($subMenu['permission']) ||
                                auth()->user()->hasPermission($subMenu['permission'], true, false))
                            <div data-kt-menu-trigger="{default:'click', lg: 'hover'}"
                                data-kt-menu-placement="right-start"
                                class="menu-item menu-lg-down-accordion {{ hasActiveSubMenu($subMenu) ? 'here show' : '' }}">
                                <!--begin:Menu link-->
                                <a target="{{ isset($subMenu['target']) ? $subMenu['target'] : '_self' }}"
                                    href="{{ checkRouteOrUrl($subMenu) }}" id="{{ $subMenu['id'] }}"
                                    class="menu-link {{ str_replace('.', '-', $subMenu['class']) }}">
                                    <span class="menu-icon">

                                        @isset($subMenu['iconElement'])
                                            {!! $subMenu['iconElement'] !!}
                                        @else
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                        @endisset

                                    </span>


                                    <span class="menu-title">
                                        @isset($subMenu['title'])
                                            {{ $subMenu['title'] }}
                                        @endisset
                                    </span>
                                    @if (isset($subMenu['sub_menu']) && is_array($subMenu['sub_menu']))
                                        <span class="menu-arrow"></span>
                                    @endif
                                </a>
                                <!--end:Menu link-->
                                @if (isset($subMenu['sub_menu']) && is_array($subMenu['sub_menu']))
                                    <!--begin:Menu sub-->
                                    <div
                                        class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown menu-active-bg px-lg-2 py-lg-4 w-lg-325px">

                                        @foreach ($subMenu['sub_menu'] as $subMenu2)
                                            @if (
                                                !isset($subMenu2['permission']) ||
                                                    auth()->user()->hasPermission($subMenu2['permission'], true, false))
                                                <!--begin:Menu item-->
                                                <div
                                                    class="menu-item {{ @$subMenu2['route_name'] == Route::currentRouteName() ? 'here show' : '' }}">
                                                    <!--begin:Menu link-->
                                                    <a target="{{ isset($subMenu2['target']) ? $subMenu2['target'] : '_self' }}"
                                                        href="{{ checkRouteOrUrl($subMenu2) }}"
                                                        id="{{ $subMenu2['id'] }}"
                                                        class="menu-link {{ str_replace('.', '-', $subMenu2['class']) }}">
                                                        <span class="menu-icon">
                                                            @isset($subMenu2['iconElement'])
                                                                {!! $subMenu2['iconElement'] !!}
                                                            @else
                                                                <span class="menu-bullet">
                                                                    <span class="bullet bullet-dot"></span>
                                                                </span>
                                                            @endisset
                                                        </span>
                                                        <span class="menu-title">{{ $subMenu2['title'] }}</span>
                                                    </a>
                                                    <!--end:Menu link-->
                                                </div>
                                                <!--end:Menu item-->
                                            @endif
                                        @endforeach
                                    </div>
                                    <!--end:Menu sub-->
                                @endif
                            </div>
                            <!--end:Menu item-->
                        @endif
                    @endforeach

                </div>
            @endif
        </div>
    @endif
@endforeach
<!--end::Menu header-->
