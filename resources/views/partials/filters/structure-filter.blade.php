<div class="mb-3">
    <label class="form-label fs-6 fw-semibold">{{ @$label ?? '<PERSON><PERSON><PERSON> theo chi nhánh' }}</label>
    <select name="branch" class="form-select" data-control="select2" {{--            data-close-on-select="true" --}}
        data-placeholder="Lựa chọn chi nhánh" data-allow-clear="true">
        <option></option>
        @foreach ($branches as $branch)
            <option value="{{ $branch->code }}"
                {{ isset($filters['branch']) && $filters['branch'] == $branch->code ? 'selected' : '' }}>
                {{ $branch->name }}</option>
        @endforeach
    </select>
</div>
@if (!isset($hiddenAgency))

    <div class="mb-3">
        {{--    <label class="form-label fs-6 fw-semibold">Hãng:</label> --}}
        <select name="agency" class="form-select" data-control="select2" data-placeholder="<PERSON><PERSON><PERSON> chọn đại lý"
            data-allow-clear="true" disabled>
            <option></option>

        </select>
    </div>
    @if (!isset($hiddenPos))
        <div class="mb-10">
            {{--    <label class="form-label fs-6 fw-semibold">Điểm bán:</label> --}}
            <select name="pos" class="form-select" data-control="select2" data-placeholder="Lựa chọn điểm bán"
                data-allow-clear="true" disabled>
                <option></option>

            </select>

        </div>
    @endif
@endif


@if (isset($spa))
    <script>
        $(document).ready(function() {
            var $branch = $('[name="branch"]');
            var $agency = $('[name="agency"]');
            var $pos = $('[name="pos"]');
            var selectedBranch = '{{ $filters['branch'] ?? '' }}';
            var selectedAgency = '{{ $filters['agency'] ?? '' }}';
            var agencyName = "{{ $agencyName }}";

            var selectedPos = '{{ $filters['pos'] ?? '' }}';
            var posName = "{{ $posName }}";

            if (selectedBranch) {
                loadAgencies();
            }

            if (selectedAgency && agencyName) {
                loadPos();
                if ($agency.find("option[value='" + selectedAgency + "']").length) {
                    $agency.val(selectedAgency).trigger('change');
                } else {
                    const newOption = new Option(agencyName, selectedAgency, true, true);
                    $agency.append(newOption).trigger('change');
                }

            }

            if (selectedPos && posName) {
                if ($pos.find("option[value='" + selectedPos + "']").length) {
                    $pos.val(selectedPos).trigger('change');
                } else {
                    const newOption = new Option(posName, selectedPos, true, true);
                    $pos.append(newOption).trigger('change');
                }

            }

            function loadAgencies() {

                if (!selectedBranch) {
                    $agency.empty().append('<option></option>').prop('disabled', true).select2();
                    return;
                }

                $agency.select2({
                        ajax: {
                            url: '{{ route('api.searchAgencies') }}', // URL của API tìm kiếm agencies dựa trên branch được chọn
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term,
                                    branchCode: selectedBranch,
                                    page: params.page
                                };
                            },
                            processResults: function(data, params) {
                                params.page = params.page || 1;
                                return {
                                    results: data.results, // Sử dụng kết quả từ API
                                    pagination: {
                                        more: data.pagination.more // Sử dụng thông tin phân trang từ API
                                    }
                                };
                            }
                        },

                    }).on('select2:select', function(e) {
                        console.log(222, "loadAgencies", e)
                    }).val(selectedAgency).trigger('change')
                    .prop('disabled', false);

            }

            function loadPos() {

                if (!selectedAgency) {
                    $('[name="pos"]').empty().append('<option></option>').prop('disabled', true).select2();
                    return;
                }
                $('[name="pos"]').select2({
                    ajax: {
                        url: '{{ route('api.searchPos') }}', // URL của API tìm kiếm POS dựa trên agency được chọn
                        delay: 250,
                        data: function(params) {
                            return {
                                q: params.term,
                                agencyCode: selectedAgency,
                                page: params.page
                            };
                        },
                        processResults: function(data, params) {
                            params.page = params.page || 1;

                            return {
                                results: data.results, // Sử dụng kết quả từ API
                                pagination: {
                                    more: data.pagination.more // Sử dụng thông tin phân trang từ API
                                }
                            };
                        }
                    },
                }).prop('disabled', false);
            }

            $branch.on('change', function() {
                const branchCode = $(this).val();

                // Clear và disable select box của Agency
                $agency.empty().append('<option></option>').prop('disabled', true).select2();

                // Clear và disable select box của Pos
                $pos.empty().append('<option></option>').prop('disabled', true).select2();
                selectedBranch = branchCode;
                loadAgencies();
            });


            $agency.on('change', function() {
                const agencyCode = $(this).val();

                $pos.empty().append('<option></option>').prop('disabled', true).select2();
                selectedAgency = agencyCode;
                loadPos();
            });


        });
    </script>
@else
    @push('script-component')
        <script>
            $(document).ready(function() {
                var $branch = $('[name="branch"]');
                var $agency = $('[name="agency"]');
                var $pos = $('[name="pos"]');
                var selectedBranch = '{{ $filters['branch'] ?? '' }}';
                var selectedAgency = '{{ $filters['agency'] ?? '' }}';
                var agencyName = "{{ $agencyName }}";

                var selectedPos = '{{ $filters['pos'] ?? '' }}';
                var posName = "{{ $posName }}";

                if (selectedBranch) {
                    loadAgencies();
                }

                if (selectedAgency && agencyName) {
                    loadPos();
                    if ($agency.find("option[value='" + selectedAgency + "']").length) {
                        $agency.val(selectedAgency).trigger('change');
                    } else {
                        const newOption = new Option(agencyName, selectedAgency, true, true);
                        $agency.append(newOption).trigger('change');
                    }

                }

                if (selectedPos && posName) {
                    if ($pos.find("option[value='" + selectedPos + "']").length) {
                        $pos.val(selectedPos).trigger('change');
                    } else {
                        const newOption = new Option(posName, selectedPos, true, true);
                        $pos.append(newOption).trigger('change');
                    }

                }

                function loadAgencies() {

                    if (!selectedBranch) {
                        $agency.empty().append('<option></option>').prop('disabled', true).select2();
                        return;
                    }

                    $agency.select2({
                            ajax: {
                                url: '{{ route('api.searchAgencies') }}', // URL của API tìm kiếm agencies dựa trên branch được chọn
                                delay: 250,
                                data: function(params) {
                                    return {
                                        q: params.term,
                                        branchCode: selectedBranch,
                                        page: params.page
                                    };
                                },
                                processResults: function(data, params) {
                                    params.page = params.page || 1;
                                    return {
                                        results: data.results, // Sử dụng kết quả từ API
                                        pagination: {
                                            more: data.pagination.more // Sử dụng thông tin phân trang từ API
                                        }
                                    };
                                }
                            },

                        }).on('select2:select', function(e) {
                            console.log(222, "loadAgencies", e)
                        }).val(selectedAgency).trigger('change')
                        .prop('disabled', false);

                }

                function loadPos() {

                    if (!selectedAgency) {
                        $('[name="pos"]').empty().append('<option></option>').prop('disabled', true).select2();
                        return;
                    }
                    $('[name="pos"]').select2({
                        ajax: {
                            url: '{{ route('api.searchPos') }}', // URL của API tìm kiếm POS dựa trên agency được chọn
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term,
                                    agencyCode: selectedAgency,
                                    page: params.page
                                };
                            },
                            processResults: function(data, params) {
                                params.page = params.page || 1;

                                return {
                                    results: data.results, // Sử dụng kết quả từ API
                                    pagination: {
                                        more: data.pagination.more // Sử dụng thông tin phân trang từ API
                                    }
                                };
                            }
                        },
                    }).prop('disabled', false);
                }

                $branch.on('change', function() {
                    const branchCode = $(this).val();

                    // Clear và disable select box của Agency
                    $agency.empty().append('<option></option>').prop('disabled', true).select2();

                    // Clear và disable select box của Pos
                    $pos.empty().append('<option></option>').prop('disabled', true).select2();
                    selectedBranch = branchCode;
                    loadAgencies();
                });


                $agency.on('change', function() {
                    const agencyCode = $(this).val();

                    $pos.empty().append('<option></option>').prop('disabled', true).select2();
                    selectedAgency = agencyCode;
                    loadPos();
                });


            });
        </script>
    @endpush
@endif
