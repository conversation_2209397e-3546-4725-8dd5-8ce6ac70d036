@props(['label' => 'Chuyên viên kinh doanh', 'disabled' => false, 'inputName' => 'staff', 'staffRole' => App\Role::ROLE_BUSINESS_STAFF, 'dropdownParent' => ''])

@php
    $showLabel = $label !== false;
@endphp

@if ($showLabel)
    <label class="form-label">{{ $label }}</label>
@endif


<select data-control="select2" data-hide-search="true" class="form-select {{ $inputName }}-select-2" name="{{ $inputName }}" data-dropdown-parent="{{ $dropdownParent }}"
    data-placeholder="Lựa chọn chuyên viên" data-allow-clear="true" @disabled($disabled)>
    @isset($filters[$inputName])
        <option value="{{ $filters[$inputName] }}">{{ @$staffName }}</option>
    @endisset
</select>


@pushOnce('script-component')
    <script>
        function setupStaffSelect2(extraParams, disabled = false, $element = $('select.{{ $inputName }}-select-2')) {
            $element.select2({
                ajax: {
                    url: '{{ route('api.searchUsers') }}',
                    delay: 250,
                    data: function(params) {
                        return Object.assign({
                            q: params.term,
                            page: params.page,
                            role: '{{ $staffRole }}',
                        }, extraParams);
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.results,
                            pagination: {
                                more: data.pagination.more
                            }
                        };
                    }
                }
            }).prop('disabled', disabled);
        }

        $(document).ready(function() {
            @if (!$disabled)
            setupStaffSelect2({}, {{ $disabled }});
            @endif
        });
    </script>
@endPushOnce
