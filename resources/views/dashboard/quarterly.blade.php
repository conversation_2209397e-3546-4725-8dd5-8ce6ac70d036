@extends('layouts.app')

@section('page-title', __('Dashboard'))
@section('page-heading', '')

@section('breadcrumbs')

@stop
@section('header-actions')

    @php
        $quarter   = \Carbon\Carbon::now()->subMonth()->format("Y")."-Q".\Carbon\Carbon::now()->subMonth()->quarter;
        $params = [
            'event_time' =>  request('event_time', $quarter),
            'type_report' => 'quarterly',
        ];
    @endphp

    @include("dashboard.partials.toolbar",$params)

@endsection

@section('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Custom quarter picker plugin for flatpickr
            function quarterSelectPlugin() {
                return function(fp) {
                    function selectQuarter(e) {
                        const quarter = e.target.getAttribute('data-quarter');
                        if (quarter) {
                            const year = fp.currentYear;
                            const month = (quarter - 1) * 3;
                            const date = new Date(year, month, 1);
                            fp.setDate(date);
                            fp.close();
                            updateInput(fp, year, quarter);
                            fp.input.dispatchEvent(new Event('change', { bubbles: true })); // Trigger change event
                        }
                    }

                    function updateInput(instance, year, quarter) {
                        instance._input.value = "Quý " + quarter + " năm " + year;
                        instance.input.value = year + "-Q" + quarter; // For form submission
                    }

                    function highlightCurrentAndSelectedQuarter(fp) {
                        const currentDate = new Date();
                        const currentQuarter = Math.floor(currentDate.getMonth() / 3) + 1;
                        const currentYear = currentDate.getFullYear();

                        fp.daysContainer.querySelectorAll('.flatpickr-monthSelect-month').forEach(function(elem) {
                            const quarter = parseInt(elem.getAttribute('data-quarter'), 10);
                            elem.classList.remove('current-quarter', 'selected-quarter');

                            if (fp.selectedDates.length > 0) {
                                const selectedDate = fp.selectedDates[0];
                                const selectedQuarter = Math.floor(selectedDate.getMonth() / 3) + 1;
                                const selectedYear = selectedDate.getFullYear();

                                if (quarter === selectedQuarter && fp.currentYear === selectedYear) {
                                    elem.classList.add('selected-quarter');
                                }
                            }

                            if (quarter === currentQuarter && fp.currentYear === currentYear) {
                                elem.classList.add('current-quarter');
                            }
                        });
                    }

                    return {
                        onReady: function() {
                            fp.calendarContainer.classList.add('quarter-picker');
                            fp.monthElements.forEach(function(elem) {
                                elem.remove();
                            });
                            fp.yearElements.forEach(function(elem) {
                                elem.style.width = '100%';
                            });

                            const quartersContainer = fp.daysContainer.parentNode;
                            quartersContainer.innerHTML = '';

                            for (let i = 1; i <= 4; i++) {
                                const quarterElem = document.createElement('div');
                                quarterElem.className = 'flatpickr-monthSelect-month';
                                quarterElem.textContent = 'Quý ' + i;
                                quarterElem.setAttribute('data-quarter', i);
                                quarterElem.addEventListener('click', selectQuarter);
                                quartersContainer.appendChild(quarterElem);
                            }

                            // Handle year navigation
                            const prevYearButton = fp.calendarContainer.querySelector('.flatpickr-prev-month');
                            const nextYearButton = fp.calendarContainer.querySelector('.flatpickr-next-month');

                            prevYearButton.addEventListener('click', function() {
                                fp.currentYear--;
                                fp.redraw();
                            });

                            nextYearButton.addEventListener('click', function() {
                                fp.currentYear++;
                                fp.redraw();
                            });

                            highlightCurrentAndSelectedQuarter(fp);
                        },
                        onChange: function(selectedDates, dateStr, instance) {
                            const date = selectedDates[0];
                            const quarter = Math.floor(date.getMonth() / 3) + 1;
                            const year = date.getFullYear();
                            updateInput(instance, year, quarter);
                            highlightCurrentAndSelectedQuarter(instance);
                        },
                        onYearChange: function() {
                            highlightCurrentAndSelectedQuarter(fp);
                        }
                    };
                };
            }

            // Initialize flatpickr with the custom quarter picker plugin
            $(document).ready(function () {
                var reportDatepicker = $('#event_time');

                var options = {
                    locale: "vn",
                    weekNumbers: false,
                    altInput: true,
                    altFormat: "Quý Q Năm Y",
                    dateFormat: "Y-Q",
                    defaultDate: parseQuarterDate($('#event_time').val()),
                    plugins: [
                        quarterSelectPlugin()
                    ],
                    onReady: function(selectedDates, dateStr, instance) {
                        const date = selectedDates[0];
                        if (date) {
                            const quarter = Math.floor(date.getMonth() / 3) + 1;
                            const year = date.getFullYear();
                            updateInput(instance, year, quarter);
                        }
                    },
                    onChange: function(selectedDates, dateStr, instance) {
                        const date = selectedDates[0];
                        if (date) {
                            const quarter = Math.floor(date.getMonth() / 3) + 1;
                            const year = date.getFullYear();
                            updateInput(instance, year, quarter);
                        }
                    }
                };

                reportDatepicker.flatpickr(options);

                function updateInput(instance, year, quarter) {
                    instance._input.value = "Quý " + quarter + " năm " + year;
                    instance.input.value = year + "-Q" + quarter; // For form submission
                }

                function parseQuarterDate(value) {
                    const match = value.match(/(\d{4})-Q(\d)/);
                    if (match) {
                        const year = parseInt(match[1], 10);
                        const quarter = parseInt(match[2], 10);
                        const month = (quarter - 1) * 3;
                        return new Date(year, month, 1);
                    }
                    return null;
                }
            });
        });

    </script>
@stop


@section('content')

    <div id="kt_app_content" class="app-content " >
        <!--begin::Row-->
        <div class="row g-5 g-xl-10 mb-5">
            @foreach (\App\Plugins\App::availableWidgets(auth()->user()) as $widget)
                @if (in_array('dashboard',$widget->show))

                    {!! app()->call([$widget, 'render']) !!}
                @endif

            @endforeach
        </div>
        <!--end::Row-->
    </div>
@stop

@section('script-vendor')

    @foreach (\App\Plugins\App::availableWidgets(auth()->user()) as $widget)
        @if (method_exists($widget, 'scripts'))
            {!! app()->call([$widget, 'scripts']) !!}
        @endif
    @endforeach

    <script src="{{ asset('assets/plugins/custom/datatables/datatables.bundle.js') }}"></script>
    <script src="{{ asset('assets/js/custom/global/setupSelect2.js') }}"></script>

    <script>
        function formatNumeric(number) {
            if (isNaN(number) || number === null || number === '') {
                return '';
            }

            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }

        jQuery(document).ready(function($) {
            $(".dataTable").DataTable({
                "scrollY": 360,
                "scrollX": false,
                "scrollCollapse": true,
                "paging": false,
                "order": [],

                "responsive": {
                    details: false
                },
                "initComplete": function(settings, json) {
                    var api = this.api();
                    api.columns().every(function() {
                        var column = this;
                        var columnIndex = column.index();
                        var isNumeric = true;

                        var orderByAscIndex = null;
                        var orderByDescIndex = null;
                        var columns = api.columns().nodes().length;
                        for (var i = 0; i < columns; i++) {
                            var columni = api.column(i);
                            var headerClass = $(columni.header()).attr('class');

                            if (headerClass && headerClass.includes('orderByASC')) {
                                orderByAscIndex = i;
                            }
                            else if (headerClass && headerClass.includes('orderByDesc')) {
                                orderByDescIndex = i;
                            }
                        }

                        // Set the initial sorting if a column with 'orderByASC' class was found
                        if (orderByAscIndex !== null) {
                            api.order([orderByAscIndex, 'asc']).draw();
                        }
                        else if (orderByDescIndex !== null) {
                            api.order([orderByDescIndex, 'desc']).draw();
                        }

                        // Kiểm tra dữ liệu của cột để xác định nếu tất cả giá trị đều là số
                        column.data().each(function (value, index) {
                            var numericValue = parseFloat(
                                value.replace(/\s+/g, '').replace(/,/g, '.')
                            );

                            if (value !== null && value !== '' && isNaN(numericValue)) {
                                isNumeric = false;
                            }

                            // Định dạng số và cập nhật nội dung ô
                            if (!isNaN(numericValue)) {
                                var formattedValue = formatNumeric(numericValue);
                                $(column.nodes()[index]).html(formattedValue);
                            }
                        });

                        // Nếu cột là numeric, áp dụng định dạng số và căn lề phải
                        if (isNumeric) {
                            console.log("number");
                            console.log(columnIndex);
                            column.nodes().to$().addClass('text-right');

                            // Thêm lớp CSS căn lề phải và in đậm
                            api.settings()[0].aoColumns[columnIndex].sClass = 'text-right';

                            // Thêm render và sort cho cột này
                            api.settings()[0].aoColumns[columnIndex].render = $.fn.dataTable.render.number('.', ',', 2, '');
                            api.settings()[0].aoColumns[columnIndex].type = 'numeric-dot-comma';
                        }
                    });
                },
                footerCallback: function (row, data, start, end, display) {
                    let api = this.api();

                    let intVal = function (i) {
                        return typeof i === 'string'
                            ? i.replace(/[^\d]/g, '') * 1
                            : typeof i === 'number'
                                ? i
                                : 0;
                    };

                    var columns = api.columns().nodes().length;
                    var hiddenFooter = true;
                    for (var i = 0; i < columns; i++) {
                        var column = api.column(i);
                        var headerClass = $(column.header()).attr('class');

                        if (headerClass && headerClass.includes('hasTotal')) {
                            hiddenFooter = false;
                            total = api
                                .column(i)
                                .data()
                                .reduce((a, b) => intVal(a) + intVal(b), 0);

                            // Update footer
                            api.column(i).footer().innerHTML =
                                '' + formatNumeric(total) + ' ';
                        }
                    }
                    if(hiddenFooter) {
                        api.column(0).footer().innerHTML = '';
                    }
                }
            });

            // Định nghĩa loại sắp xếp tùy chỉnh cho các số có dấu chấm cho hàng nghìn và dấu phẩy cho phần thập phân
            $.extend($.fn.dataTable.ext.type.order, {
                "numeric-dot-comma-pre": function(data) {
                    return parseFloat(data.replace(/\./g, '').replace(',', '.'));
                },

                "numeric-dot-comma-asc": function(a, b) {
                    return a - b;
                },

                "numeric-dot-comma-desc": function(a, b) {
                    return b - a;
                }
            });
        });
    </script>
    @include('partials.game-color')
@stop
