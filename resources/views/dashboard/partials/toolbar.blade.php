@props(['webview' => request()->has('webview') ? 1 : 0])
<div class="form-group container-fluid" id="dashboard_filter">
    <form id="date_form" action="{{ url()->current() }}" method="GET">
        @php

            $last_updated_time = Cache::tags('dashboard')->remember('last_updated_time', now()->addHour(), function () {
                return \Modules\Report\app\Models\PosDaily::whereNotNull('event_date')
                    ->orderBy('event_date', 'DESC')
                    ->value('event_date');
            });

            $update_time = \Carbon\Carbon::parse($last_updated_time);

            $typeReport = request('type_report', 'daily');

            if ($typeReport == 'monthly') {
                $eventTime = request('event_time', \Carbon\Carbon::now()->subMonth()->format('Y-m'));
                $timeStr = explode('-', $eventTime);
                $eventTime = \Carbon\Carbon::create($timeStr[0], $timeStr[1])
                    ->startOfMonth()
                    ->format('Y-m-d');
                $endTime = \Carbon\Carbon::create($timeStr[0], $timeStr[1])
                    ->endOfMonth()
                    ->format('Y-m-d');
            } elseif ($typeReport == 'daily') {
                $eventTime = request('event_time', \Carbon\Carbon::now()->subDay()->format('Y-m-d'));
                $eventTime = \Carbon\Carbon::createFromFormat('Y-m-d', $eventTime)->format('Y-m-d');
                $endTime = \Carbon\Carbon::create($eventTime)->endOfDay()->format('Y-m-d');
            } elseif ($typeReport == 'weekly') {
                $eventTime = request('event_time', \Carbon\Carbon::now()->subDay()->format('Y-W'));
                $timeStr = explode('-', $eventTime);
                $eventTime = \Carbon\Carbon::create($timeStr[0], 6, 1)
                    ->week($timeStr[1])
                    ->startOfWeek(\Carbon\Carbon::MONDAY)
                    ->format('Y-m-d');
                $endTime = \Carbon\Carbon::create($timeStr[0], 6, 1)
                    ->week($timeStr[1])
                    ->endOfWeek(\Carbon\Carbon::SUNDAY)
                    ->format('Y-m-d');
            } else {
                $eventTime = request('event_time', \Carbon\Carbon::now()->startOfMonth()->format('Y-m-d'));
                $endTime = \Carbon\Carbon::now()->endOfMonth()->format('Y-m-d');
            }
        @endphp
        @if ($webview == 1)
            <div class="card-toolbar">
                <!--begin::Toolbar-->
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-12 col-sm-7 col-md-8 mb-1">

                                @include('partials.filters.select2.BranchMultiSelect', [
                                    'filters' => ['branch' => request()->input('branch')],
                                    'closeOnSelect' => true,
                                    'label' => false,
                                ])
                            </div>
                            <div class="col-6 col-sm-3 col-md-2 mb-1">
                                <input id="event_time" name="event_time" type="text"
                                    class="form-control form-control-solid-bg me-3" style="min-width: 350px"
                                    value="{{ $event_time }}" />
                                <input name="type_report" value="{{ $type_report }}" type="hidden">
                            </div>

                            <div class="col-6 col-sm-2  col-md-2  col-lg-1  mb-1">
                                <button type="submit" class="btn btn-primary fw-semibold " style="width: 100%"><i
                                        class="fa fa-filter"></i> {{ __('Xem') }}</button>
                            </div>
                            <div class="col-sm-12 col-md-12 col-lg-3  mb-1 "
                                style="text-align: right; line-height: 42px;">
                                Dữ liệu được cập nhật hết ngày: {{ $update_time->format('d/m/Y') }}

                            </div>
                        </div>

                    </div>

                    <div class="col-md-1 ">

                    </div>
                </div>

            </div>

            <input type="hidden" name="webview" value="1">
        @else
            <div class="card-toolbar">
                <!--begin::Toolbar-->
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">

                            <div class="col-12 col-sm-3 col-md-5 col-lg-2 col-xl-3 mb-1">
                                <input id="event_time" name="event_time" type="text"
                                    class="form-control form-control-solid-bg me-3" style="min-width: 350px"
                                    value="{{ $event_time }}" />
                                <input name="type_report" value="{{ $type_report }}" type="hidden">
                            </div>
                            <div class="col-12 col-sm-7 col-md-5 col-lg-4 col-xl-4 mb-1">

                                @include('partials.filters.select2.BranchMultiSelect', [
                                    'filters' => ['branch' => request()->input('branch')],
                                    'closeOnSelect' => true,
                                    'label' => false,
                                ])
                            </div>
                            <div class="col-12 col-sm-2  col-md-2  col-lg-2 col-xl-2 mb-1">
                                <button type="submit" class="btn btn-primary fw-semibold " style="width: 100%"><i
                                        class="fa fa-filter"></i> {{ __('Xem') }}</button>
                            </div>
                            <div class="col-sm-12 col-md-12  col-lg-4 col-xl-3 mb-1 "
                                style="text-align: right; line-height: 42px;">
                                Dữ liệu được cập nhật hết ngày: {{ $update_time->format('d/m/Y') }}

                            </div>
                        </div>

                    </div>

                    <div class="col-md-1 ">

                    </div>
                </div>

            </div>
        @endif

    </form>
</div>
@if ($webview == 0)
    <div class="app-engage " id="kt_app_engage">

        <a href="{{ route('dashboard.daily', array_merge(request()->all(), ['type_report' => 'daily', 'event_time' => null])) }}"
            class="app-engage-btn hover-success">
            <i class="ki-duotone ki-chart-pie-4 fs-2 pt-1 mb-2"><span class="path1"></span><span
                    class="path2"></span><span class="path3"></span><span class="path4"></span></i>
            Ngày
        </a>

        <a href="{{ route('dashboard.weekly', array_merge(request()->all(), ['type_report' => 'weekly', 'event_time' => null])) }}"
            class="app-engage-btn hover-success">
            <i class="ki-duotone ki-calendar fs-1 pt-1 mb-2"><span class="path1"></span><span
                    class="path2"></span></i>
            Tuần
        </a>

        <a href="{{ route('dashboard.monthly', array_merge(request()->all(), ['type_report' => 'monthly', 'event_time' => null])) }}"
            class="app-engage-btn hover-success">
            <i class="ki-duotone ki-calendar-2 fs-1 pt-1 mb-2"><span class="path1"></span><span
                    class="path2"></span></i>
            Tháng
        </a>
        <a href="{{ route('dashboard.quarterly', array_merge(request()->all(), ['type_report' => 'quarterly', 'event_time' => null])) }}"
            class="app-engage-btn hover-success">
            <i class="ki-duotone  ki-chart-line-up-2  fs-1 pt-1 mb-2"><span class="path1"></span><span
                    class="path2"></span></i>
            Quý
        </a>


        <!--begin::Engage close-->
        <a href="#" id="kt_app_engage_toggle_off"
            class="app-engage-btn app-engage-btn-toggle-off text-hover-primary p-0">
            <i class="ki-duotone ki-cross fs-2x"><span class="path1"></span><span class="path2"></span></i>
        </a>
        <!--end::Engage close-->

        <!--begin::Engage close-->
        <a href="#" id="kt_app_engage_toggle_on"
            class="app-engage-btn app-engage-btn-toggle-on text-hover-primary p-0" data-bs-toggle="tooltip"
            data-bs-placement="left" data-bs-custom-class="tooltip-inverse" data-bs-dimiss="click"
            aria-label="Explore Metronic" data-bs-original-title="Explore Metronic" data-kt-initialized="1">
            <i class="ki-duotone ki-switch fs-2 text-primary"><span class="path1"></span><span
                    class="path2"></span><span class="path3"></span></i>
        </a>
        <!--end::Engage close-->
    </div>
@endif
