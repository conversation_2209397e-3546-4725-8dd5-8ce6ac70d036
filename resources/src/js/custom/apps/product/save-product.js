"use strict";

// Class definition
var KTAppEcommerceSaveProduct = (function () {
    // Get elements
    const form = document.getElementById("kt_ecommerce_add_product_form");
    const submitButton = document.getElementById(
        "kt_ecommerce_add_product_submit"
    );

    const routeStore = form.getAttribute("data-kt-store");

    var myDropzone;

    let contentQuill;

    // Private functions

    // Init tagify
    const initTagify = () => {
        // Define all elements for tagify
        const elements = [
            "#kt_ecommerce_add_product_category",
            "#kt_ecommerce_add_product_tags",
        ];

        // Loop all elements
        elements.forEach((element) => {
            // Get tagify element
            const tagify = document.querySelector(element);

            // Break if element not found
            if (!tagify) {
                return;
            }

            // Init tagify --- more info: https://yaireo.github.io/tagify/
            new Tagify(tagify, {
                whitelist: [
                    "new",
                    "trending",
                    "sale",
                    "discounted",
                    "selling fast",
                    "last 10",
                ],
                dropdown: {
                    maxItems: 20, // <- mixumum allowed rendered suggestions
                    classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                    enabled: 0, // <- show suggestions on focus
                    closeOnSelect: false, // <- do not hide the suggestions dropdown once an item has been selected
                },
            });
        });
    };

    // Init form repeater --- more info: https://github.com/DubFriend/jquery.repeater
    const initFormRepeater = () => {
        $("#kt_ecommerce_add_product_options").repeater({
            initEmpty: false,

            defaultValues: {
                "text-input": "foo",
            },

            show: function () {
                $(this).slideDown();

                // Init select2 on new repeated items
                initConditionsSelect2();
            },

            hide: function (deleteElement) {
                $(this).slideUp(deleteElement);
            },
        });
    };

    // Init condition select2
    const initConditionsSelect2 = () => {
        // Tnit new repeating condition types
        const allConditionTypes = document.querySelectorAll(
            '[data-kt-ecommerce-catalog-add-product="product_option"]'
        );
        allConditionTypes.forEach((type) => {
            if ($(type).hasClass("select2-hidden-accessible")) {
                return;
            } else {
                $(type).select2({
                    minimumResultsForSearch: -1,
                });
            }
        });
    };

    // Init noUIslider
    const initSlider = () => {
        var slider = document.querySelector(
            "#kt_ecommerce_add_product_discount_slider"
        );
        var value = document.querySelector(
            "#kt_ecommerce_add_product_discount_label"
        );

        noUiSlider.create(slider, {
            start: [10],
            connect: true,
            range: {
                min: 1,
                max: 100,
            },
        });

        slider.noUiSlider.on("update", function (values, handle) {
            value.innerHTML = Math.round(values[handle]);
            if (handle) {
                value.innerHTML = Math.round(values[handle]);
            }
        });
    };

    // Init quill editor
    const initQuill = () => {
        // Define all elements for quill editor
        const elements = [
            "#kt_ecommerce_add_product_description",
            "#kt_ecommerce_add_product_meta_description",
        ];

        // Loop all elements
        elements.forEach((element) => {
            // Get quill element
            let quill = document.querySelector(element);

            // Break if element not found
            if (!quill) {
                return;
            }

            // Init quill --- more info: https://quilljs.com/docs/quickstart/
            quill = new Quill(element, {
                modules: {
                    toolbar: [
                        [
                            {
                                header: [1, 2, false],
                            },
                        ],
                        ["bold", "italic", "underline"],
                        ["image", "code-block"],
                    ],
                },
                placeholder: "Nhập mô tả ở đây...",
                theme: "snow", // or 'bubble'
            });
        });
    };

    // Khởi tạo DropzoneJS
    const initDropzone = () => {
        myDropzone = new Dropzone("#kt_ecommerce_add_product_media", {
            url: routeStore, // Set the url for your upload script location
            paramName: "image", // The name that will be used to transfer the file
            method: "POST",
            maxFiles: 10,
            maxFilesize: 10, // MB
            addRemoveLinks: true,
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            accept: function (file, done) {
                if (file.name == "wow.jpg") {
                    done("Naha, you don't.");
                } else {
                    done();
                }
            },
            renameFile: function (file) {
                var dt = new Date();
                var time = dt.getTime();
                return time + file.name;
            },
            autoProcessQueue: false, // Ngăn Dropzone tự động tải lên tệp tin khi được chọn
        });
        // Xử lý sự kiện khi người dùng thêm file
        // myDropzone.on("addedfile", function (file) {
        //     // Xử lý tệp tin ở đây mà không cần gửi lên máy chủ
        //     console.log("Đã thêm file:", file);
        // });
    };

    // Submit form handler
    const handleSubmit = () => {
        // Define variables
        let validator;

        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
        validator = FormValidation.formValidation(form, {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: "Tên là bắt buộc",
                        },
                    },
                },
                code: {
                    validators: {
                        notEmpty: {
                            message: "Mã là bắt buộc",
                        },
                    },
                },
                price: {
                    validators: {
                        notEmpty: {
                            message: "Giá trang thiết bị là bắt buộc",
                        },
                    },
                },
                cost: {
                    validators: {
                        notEmpty: {
                            message: "Giá trang thiết bị là bắt buộc",
                        },
                    },
                },
                category_id: {
                    validators: {
                        notEmpty: {
                            message: "Danh mục là bắt buộc",
                        },
                    },
                },
                unit_id: {
                    validators: {
                        notEmpty: {
                            message: "Đơn vị là bắt buộc",
                        },
                    },
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap5({
                    rowSelector: ".fv-row",
                    eleInvalidClass: "",
                    eleValidClass: "",
                }),
            },
        });

        // Handle submit button
        submitButton.addEventListener("click", (e) => {
            e.preventDefault();

            // Validate form before submit
            if (validator) {
                validator.validate().then(function (status) {
                    console.log("validated!");

                    if (status == "Valid") {
                        submitButton.setAttribute("data-kt-indicator", "on");

                        // Disable submit button whilst loading
                        submitButton.disabled = true;

                        submitButton.removeAttribute("data-kt-indicator");

                        // Lấy giá trị của trường unit_id từ form
                        const unitId = form.querySelector(
                            "select[name='unit_id']"
                        );

                        // Thu thập dữ liệu từ Quill Editor
                        let quill = new Quill(
                            "#kt_ecommerce_add_product_description"
                        );

                        // Lấy nội dung HTML từ Quill Editor
                        let quillContent = quill.root.innerHTML;

                        // Thu thập dữ liệu từ Dropzone
                        var formData = new FormData();

                        // var files = myDropzone.getQueuedFiles();

                        // console.log(files);
                        // // Thêm mảng chứa các file vào form với tên "images"
                        // formData.append("image", files);

                        // Thu thập dữ liệu từ form
                        var data = new FormData(form);
                        for (var pair of data.entries()) {
                            formData.append(pair[0], pair[1]);
                        }
                        formData.append("product_details", quillContent);
                        formData.append("starting_date", "");
                        formData.append("last_date", "");
                        formData.append("purchase_unit_id", unitId.value);
                        formData.append("sale_unit_id", unitId.value);

                        // Gửi dữ liệu lên máy chủ
                        $.ajax({
                            url: routeStore, // Lấy url từ form
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            headers: {
                                "X-CSRF-TOKEN": $(
                                    'meta[name="csrf-token"]'
                                ).attr("content"),
                            },
                            success: function (response) {
                                submitButton.removeAttribute(
                                    "data-kt-indicator"
                                );

                                Swal.fire({
                                    toast: true,
                                    icon: "success",
                                    title: "Biểu mẫu đã được gửi thành công!",
                                    position: "top-end",
                                    showConfirmButton: false,
                                    timer: 3000,
                                    iconColor: "green",
                                    showClass: {
                                        popup: "animate__animated animate__fadeInDown",
                                    },
                                    hideClass: {
                                        popup: "animate__animated animate__fadeOutUp",
                                    },
                                    didOpen: (toast) => {
                                        toast.addEventListener(
                                            "mouseenter",
                                            Swal.stopTimer
                                        );
                                        toast.addEventListener(
                                            "mouseleave",
                                            Swal.resumeTimer
                                        );
                                    },
                                }).then(function () {
                                    window.location =
                                        form.getAttribute("data-kt-redirect");
                                });
                            },
                            error: function (response) {
                                console.error("Error:", response);

                                Swal.fire({
                                    toast: true,
                                    icon: "error",
                                    title: response.responseJSON.message,
                                    position: "top-end",
                                    showConfirmButton: false,
                                    timer: 3000,
                                    iconColor: "red",
                                    showClass: {
                                        popup: "animate__animated animate__fadeInDown",
                                    },
                                    hideClass: {
                                        popup: "animate__animated animate__fadeOutUp",
                                    },
                                    didOpen: (toast) => {
                                        toast.addEventListener(
                                            "mouseenter",
                                            Swal.stopTimer
                                        );
                                        toast.addEventListener(
                                            "mouseleave",
                                            Swal.resumeTimer
                                        );
                                    },
                                })

                                submitButton.disabled = false;

                                submitButton.addAttribute("data-kt-indicator");
                            },
                        });
                    }
                });
            }
        });
    };

    // Handle discount options
    const handleDiscount = () => {
        const discountOptions = document.querySelectorAll(
            'input[name="discount_option"]'
        );
        const percentageEl = document.getElementById(
            "kt_ecommerce_add_product_discount_percentage"
        );
        const fixedEl = document.getElementById(
            "kt_ecommerce_add_product_discount_fixed"
        );

        discountOptions.forEach((option) => {
            option.addEventListener("change", (e) => {
                const value = e.target.value;

                switch (value) {
                    case "2": {
                        percentageEl.classList.remove("d-none");
                        fixedEl.classList.add("d-none");
                        break;
                    }
                    case "3": {
                        percentageEl.classList.add("d-none");
                        fixedEl.classList.remove("d-none");
                        break;
                    }
                    default: {
                        percentageEl.classList.add("d-none");
                        fixedEl.classList.add("d-none");
                        break;
                    }
                }
            });
        });
    };

    // Shipping option handler
    const handleShipping = () => {
        const shippingOption = document.getElementById(
            "kt_ecommerce_add_product_shipping_checkbox"
        );
        const shippingForm = document.getElementById(
            "kt_ecommerce_add_product_shipping"
        );

        shippingOption.addEventListener("change", (e) => {
            const value = e.target.checked;

            if (value) {
                shippingForm.classList.remove("d-none");
            } else {
                shippingForm.classList.add("d-none");
            }
        });
    };

    // Category status handler
    const handleStatus = () => {
        const target = document.getElementById(
            "kt_ecommerce_add_product_status"
        );
        const select = document.getElementById(
            "kt_ecommerce_add_product_status_select"
        );
        const statusClasses = ["bg-success", "bg-warning", "bg-danger"];

        $(select).on("change", function (e) {
            const value = e.target.value;

            switch (value) {
                case "published": {
                    target.classList.remove(...statusClasses);
                    target.classList.add("bg-success");
                    hideDatepicker();
                    break;
                }
                case "scheduled": {
                    target.classList.remove(...statusClasses);
                    target.classList.add("bg-warning");
                    showDatepicker();
                    break;
                }
                case "inactive": {
                    target.classList.remove(...statusClasses);
                    target.classList.add("bg-danger");
                    hideDatepicker();
                    break;
                }
                case "draft": {
                    target.classList.remove(...statusClasses);
                    target.classList.add("bg-primary");
                    hideDatepicker();
                    break;
                }
                default:
                    break;
            }
        });

        // Handle datepicker
        const datepicker = document.getElementById(
            "kt_ecommerce_add_product_status_datepicker"
        );

        // Init flatpickr --- more info: https://flatpickr.js.org/
        $("#kt_ecommerce_add_product_status_datepicker").flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
        });

        const showDatepicker = () => {
            datepicker.parentNode.classList.remove("d-none");
        };

        const hideDatepicker = () => {
            datepicker.parentNode.classList.add("d-none");
        };
    };

    // Condition type handler
    const handleConditions = () => {
        const allConditions = document.querySelectorAll(
            '[name="method"][type="radio"]'
        );
        const conditionMatch = document.querySelector(
            '[data-kt-ecommerce-catalog-add-category="auto-options"]'
        );
        allConditions.forEach((radio) => {
            radio.addEventListener("change", (e) => {
                if (e.target.value === "1") {
                    conditionMatch.classList.remove("d-none");
                } else {
                    conditionMatch.classList.add("d-none");
                }
            });
        });
    };

    // Public methods
    return {
        init: function () {
            // Init forms
            initQuill();
            initTagify();
            initSlider();
            initFormRepeater();
            initDropzone();
            initConditionsSelect2();

            // Handle forms
            // handleStatus();
            // handleConditions();
            // handleDiscount();
            // handleShipping();
            handleSubmit();
        },
    };
})();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    KTAppEcommerceSaveProduct.init();
});
