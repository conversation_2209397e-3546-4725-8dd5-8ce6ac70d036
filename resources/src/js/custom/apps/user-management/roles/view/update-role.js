"use strict";

// Class definition
var KTUsersUpdatePermissions = (function () {
    // Shared variables
    const element = document.getElementById("kt_modal_update_role");
    const form = element.querySelector("#kt_modal_update_role_form");
    const modal = new bootstrap.Modal(element);

    // Init add schedule modal
    var initUpdatePermissions = () => {
        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
        var validator = FormValidation.formValidation(form, {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: "Tên vai trò là bắt buộc"
                        },
                    },
                },
            },

            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap5({
                    rowSelector: ".fv-row",
                    eleInvalidClass: "",
                    eleValidClass: "",
                }),
            },
        });
        //SUbmit

        const submitButton = element.querySelector(
            '[data-kt-roles-modal-action="submit"]'
        );
        submitButton.addEventListener("click", function (e) {
            e.preventDefault();

            if (validator) {
                validator.validate().then(function (status) {
                    if (status == "Valid") {
                        submitButton.setAttribute("data-kt-indicator", "on");
                        submitButton.disabled = true;

                        const formData = {
                            name: $('[name="name"]').val(),
                            display_name: $('[name="display_name"]').val(),
                            description: $('#update_role_description').val(),
                            _token: $('meta[name="csrf-token"]').attr(
                                "content"
                            ),
                            role_id: $('[name="role_id"]').val(),
                            roles: []

                        };

                        const checkboxes = $(
                            'input[type="checkbox"][name="roles[]"]'
                        );

                        checkboxes.each(function () {
                            formData["roles"].push({
                                value: $(this).val(),
                                checked: $(this).prop("checked"),
                            });
                        });

                        axios.put(
                                document
                                    .getElementById(
                                        "kt_modal_update_role_form"
                                    )
                                    .getAttribute("action"),
                                formData
                            )
                        .then(function (response) {

                            submitButton.removeAttribute(
                                "data-kt-indicator"
                            );
                            submitButton.disabled = false;

                            Swal.fire({
                                text: response.data.success,
                                icon: "success",
                                buttonsStyling: false,
                                confirmButtonText: "Đồng ý ",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            }).then(function() {
                                location.reload();
                            });
                        })
                        .catch(function (error) {
                            submitButton.removeAttribute(
                                "data-kt-indicator"
                            );
                            submitButton.disabled = false;

                            Swal.fire({
                                text: "Đã xảy ra lỗi khi gửi biểu mẫu. Vui lòng thử lại.",
                                icon: "error",
                                buttonsStyling: false,
                                confirmButtonText: "Đồng ý ",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        });

                        //     type: "PUT",
                        //     cache: false,
                        //     url: document
                        //         .getElementById("kt_modal_update_role_form")
                        //         .getAttribute("action"),
                        //     // url: submitButton.closest("form").getAttribute("action"),
                        //     data: formData,
                        //     /*    headers: {
                        //             'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        //         },*/
                        //     success: function (response) {
                        //         console.log(response);
                        //         $("#name").val(response.name);
                        //         $("#display_name").val(response.display_name);
                        //         $("#description").val(response.description);
                        //         $.ajaxSetup({ cache: false });
                        //         submitButton.removeAttribute(
                        //             "data-kt-indicator"
                        //         );
                        //         submitButton.disabled = false;

                        //         Swal.fire({
                        //             text: "Form has been successfully submitted!",
                        //             icon: "success",
                        //             buttonsStyling: false,
                        //             confirmButtonText: "Đồng ý ",
                        //             customClass: {
                        //                 confirmButton: "btn btn-primary",
                        //             },
                        //         }).then(function (result) {
                        //             if (result.isConfirmed) {
                        //                 modal.hide();
                        //             }
                        //         });
                        //     },
                        //     error: function (error) {
                        //         submitButton.removeAttribute(
                        //             "data-kt-indicator"
                        //         );
                        //         submitButton.disabled = false;

                        //         Swal.fire({
                        //             text: "An error occurred while submitting the form. Please try again.",
                        //             icon: "error",
                        //             buttonsStyling: false,
                        //             confirmButtonText: "Đồng ý ",
                        //             customClass: {
                        //                 confirmButton: "btn btn-primary",
                        //             },
                        //         });
                        //     },
                        // });
                    } else {
                        Swal.fire({
                            text: "Rất tiếc, có vẻ như đã phát hiện được một số lỗi. Vui lòng thử lại.",
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Đồng ý ",
                            customClass: {
                                confirmButton: "btn btn-primary",
                            },
                        });
                    }
                });
            }
        });

        // Close button handler
        const closeButton = element.querySelector(
            '[data-kt-roles-modal-action="close"]'
        );
        closeButton.addEventListener("click", (e) => {
            e.preventDefault();

            Swal.fire({
                text: "Bạn có chắc chắn muốn đóng không?",
                icon: "warning",
                showCancelButton: true,
                buttonsStyling: false,
                confirmButtonText: "Đồng ý",
                cancelButtonText: "Quay lại",
                customClass: {
                    confirmButton: "btn btn-primary",
                    cancelButton: "btn btn-active-light",
                },
            }).then(function (result) {
                if (result.value) {
                    modal.hide(); // Hide modal
                }
            });
        });

        // Cancel button handler
        const cancelButton = element.querySelector(
            '[data-kt-roles-modal-action="cancel"]'
        );
        cancelButton.addEventListener("click", (e) => {
            e.preventDefault();

            Swal.fire({
                text: "Bạn có chắc chắn muốn hủy không?",
                icon: "warning",
                showCancelButton: true,
                buttonsStyling: false,
                confirmButtonText: "Vâng,đóng nó lại",
                cancelButtonText: "Quay lại",
                customClass: {
                    confirmButton: "btn btn-primary",
                    cancelButton: "btn btn-active-light",
                },
            }).then(function (result) {
                if (result.value) {
                    form.reset(); // Reset form
                    modal.hide(); // Hide modal
                } else if (result.dismiss === "cancel") {
                    Swal.fire({
                        text: "Biểu mẫu của bạn chưa bị hủy!.",
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Đồng ý ",
                        customClass: {
                            confirmButton: "btn btn-primary",
                        },
                    });
                }
            });
        });

        // Submit button handler
        /*  const submitButton = element.querySelector('[data-kt-roles-modal-action="submit"]');
          submitButton.addEventListener('click', function (e) {
              // Prevent default button action
              e.preventDefault();

              // Validate form before submit
              if (validator) {
                  validator.validate().then(function (status) {
                      console.log('validated!');

                      if (status == 'Valid') {
                          // Show loading indication
                          submitButton.setAttribute('data-kt-indicator', 'on');

                          // Disable button to avoid multiple click
                          submitButton.disabled = true;

                          // Simulate form submission. For more info check the plugin's official documentation: https://sweetalert2.github.io/
                          setTimeout(function () {
                              // Remove loading indication
                              submitButton.removeAttribute('data-kt-indicator');

                              // Enable button
                              submitButton.disabled = false;

                              // Show popup confirmation
                              Swal.fire({
                                  text: "Form has been successfully submitted!",
                                  icon: "success",
                                  buttonsStyling: false,
                                  confirmButtonText: "Đồng ý ",
                                  customClass: {
                                      confirmButton: "btn btn-primary"
                                  }
                              }).then(function (result) {
                                  if (result.isConfirmed) {
                                      modal.hide();
                                  }
                              });

                              //form.submit(); // Submit form
                          }, 2000);
                      } else {
                          // Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/
                          Swal.fire({
                              text: "Rất tiếc, có vẻ như đã phát hiện một số lỗi, vui lòng thử lại.",
                              icon: "error",
                              buttonsStyling: false,
                              confirmButtonText: "Đồng ý ",
                              customClass: {
                                  confirmButton: "btn btn-primary"
                              }
                          });
                      }
                  });
              }
          // });*/
        /*      const form = document.getElementById('kt_modal_update_role_form');
             const formData = new FormData(form);
             const submitButton = element.querySelector('[data-kt-roles-modal-action="submit"]');
             submitButton.addEventListener('click', function (e) {

                 form.addEventListener('submit', function (e) {
                     e.preventDefault();
                 });

                 // Validate form before submit
                 if (validator) {
                     validator.validate().then(function (status) {
                         console.log('validated!');
                         if (status === 'Valid') {
                             submitButton.setAttribute('data-kt-indicator', 'on');
                             submitButton.disabled = true;
                             axios({
                                 method: "put",
                                 url: submitButton.closest("form").getAttribute("action"),
                                 data: formData,
                                 headers: {
                                     'X-Requested-With': 'XMLHttpRequest'
                                 }
                             })
                                 .then(function (response) {
                                     console.log(response);
                                     console.log(response.data);
                                     if (response && response.status >= 200 && response.status < 300) {
                                         // Update UI, e.g., hide loading indication
                                         submitButton.disabled = false;
                                         submitButton.setAttribute("data-kt-indicator", "off");

                                         // Show success message using Swal
                                         Swal.fire({
                                             text: response.data.success,
                                             icon: "success",
                                             buttonsStyling: false,
                                             confirmButtonText: "Đồng ý ",
                                             customClass: {
                                                 confirmButton: "btn btn-primary",
                                             },
                                         }).then(function (result) {
                                             if (result.isConfirmed) {
                                                 // Close modal or perform any other action
                                                 modal.hide();
                                             }
                                         });
                                     } else {
                                         // Handle unexpected response format
                                         console.error("Invalid response format:", response);
                                     }
                                 })

                                 .catch(function (error) {
                                     submitButton.disabled = false;
                                     submitButton.setAttribute("data-kt-indicator", "off");

                                     if (error.response && error.response.data && error.response.data.errors) {
                                         // Xử lý lỗi kiểm tra hợp lệ
                                         const validationErrors = error.response.data.errors;
                                         console.error("Lỗi Name Rỗng Catch:", validationErrors);

                                         // Hiển thị lỗi kiểm tra hợp lệ cho người dùng (bạn có thể tùy chỉnh phần này)
                                         Swal.fire({
                                             title: 'Lỗi kiểm tra hợp lệ',
                                             icon: 'error',
                                             html: Object.values(validationErrors).flat().join('<br>'),
                                             buttonsStyling: false,
                                             confirmButtonText: "Ok, hiểu rồi!",
                                             customClass: {
                                                 confirmButton: "btn btn-primary",
                                             },
                                         });
                                     } else {
                                         // Xử lý các loại lỗi khác
                                         console.error("Lỗi khác:", error.response.data);
                                         Swal.fire({
                                             text: "Đã xảy ra một lỗi không mong muốn. Vui lòng thử lại.",
                                             icon: "error",
                                             buttonsStyling: false,
                                             confirmButtonText: "Ok, hiểu rồi!",
                                             customClass: {
                                                 confirmButton: "btn btn-primary",
                                             },
                                         });
                                     }
                                 });
                         } else {
                             // Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/
                             Swal.fire({
                                 text: "Rất tiếc, có vẻ như đã phát hiện một số lỗi, vui lòng thử lại.",
                                 icon: "error",
                                 buttonsStyling: false,
                                 confirmButtonText: "Đồng ý ",
                                 customClass: {
                                     confirmButton: "btn btn-primary"
                                 }
                             });
                         }
                     });
                 }
             });*/
    };

    // Select all handler
    const handleSelectAll = () => {
        // Define variables
        const selectAll = form.querySelector("#kt_roles_select_all");
        const allCheckboxes = form.querySelectorAll('[type="checkbox"]');

        // Handle check state
        selectAll.addEventListener("change", (e) => {
            // Apply check state to all checkboxes
            allCheckboxes.forEach((c) => {
                c.checked = e.target.checked;
            });
        });
    };

    return {
        // Public functions
        init: function () {
            initUpdatePermissions();
            handleSelectAll();
        },
    };
})();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    KTUsersUpdatePermissions.init();
});
