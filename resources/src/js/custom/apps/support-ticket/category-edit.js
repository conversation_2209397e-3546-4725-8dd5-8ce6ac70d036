
    $(document).ready(function() {
    $('.edit-category').on('click', function() {
        var categoryId = $(this).data('id');
        var categoryName = $(this).data('name');
        var form = $('#kt_ecommerce_update_product_form');
        var action = form.attr('action');
        action = action.replace(':id', categoryId);
        $('input[name="name"]').val(categoryName);
        var roles = $(this).data('role');
        var roleIds = roles.map(role => role.id);
        $('.categoryTicket').val(roleIds).trigger('change');
        form.attr('action', action);
    });
});

    $('#cate-type-modal').on('show.bs.modal', function (e) {
    var form = $(this).find('form');
    form.trigger('reset');
    $('.categoryTicket').val([]).trigger('change'); // Reset giá trị của thẻ select

});
