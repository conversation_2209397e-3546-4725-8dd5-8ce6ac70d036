/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-password-strength
 * @version 2.4.0
 */

!function(t,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],o):((t="undefined"!=typeof globalThis?globalThis:t||self).FormValidation=t.FormValidation||{},t.FormValidation.plugins=t.FormValidation.plugins||{},t.FormValidation.plugins.PasswordStrength=o(t.FormValidation))}(this,(function(t){"use strict";var o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,o){t.__proto__=o}||function(t,o){for(var e in o)Object.prototype.hasOwnProperty.call(o,e)&&(t[e]=o[e])},o(t,e)};return function(t){function e(o){var e=t.call(this,o)||this;return e.opts=Object.assign({},{minimalScore:3,onValidated:function(){}},o),e.validatePassword=e.checkPasswordStrength.bind(e),e.validatorValidatedHandler=e.onValidatorValidated.bind(e),e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(e,t),e.prototype.install=function(){var t;this.core.registerValidator(e.PASSWORD_STRENGTH_VALIDATOR,this.validatePassword),this.core.on("core.validator.validated",this.validatorValidatedHandler),this.core.addField(this.opts.field,{validators:(t={},t[e.PASSWORD_STRENGTH_VALIDATOR]={message:this.opts.message,minimalScore:this.opts.minimalScore},t)})},e.prototype.uninstall=function(){this.core.off("core.validator.validated",this.validatorValidatedHandler),this.core.disableValidator(this.opts.field,e.PASSWORD_STRENGTH_VALIDATOR)},e.prototype.onEnabled=function(){this.core.enableValidator(this.opts.field,e.PASSWORD_STRENGTH_VALIDATOR)},e.prototype.onDisabled=function(){this.core.disableValidator(this.opts.field,e.PASSWORD_STRENGTH_VALIDATOR)},e.prototype.checkPasswordStrength=function(){var t=this;return{validate:function(o){var e=o.value;if(""===e)return{valid:!0};var i=zxcvbn(e),a=i.score,r=i.feedback.warning||"The password is weak";return a<t.opts.minimalScore?{message:r,meta:{message:r,score:a},valid:!1}:{meta:{message:r,score:a},valid:!0}}}},e.prototype.onValidatorValidated=function(t){if(this.isEnabled&&t.field===this.opts.field&&t.validator===e.PASSWORD_STRENGTH_VALIDATOR&&t.result.meta){var o=t.result.meta.message,i=t.result.meta.score;this.opts.onValidated(t.result.valid,o,i)}},e.PASSWORD_STRENGTH_VALIDATOR="___PasswordStrengthValidator",e}(t.Plugin)}));
