.bg-lighter {
    background-color: $bg-lighter;
}

.border-bottom-light {
  border-bottom: 1px solid $body-bg;
}

.no-decoration {
  text-decoration: none !important;
}

.flex-1 {
  flex: 1;
}

.my-10p {
  margin-top: 10%;
  margin-bottom: 10%;
}

.text-gray-500 {
  color: $gray-500;
}

.badge-lg {
  padding: 5px 8px;
}

.min-width-200 {
  min-width: 200px;
}

.min-width-150 {
  min-width: 150px;
}

.min-width-100 {
  min-width: 100px;
}

.min-width-80 {
  min-width: 80px;
}

.line-height-1 {
    line-height: 1;
}

.overflow-hidden {
    overflow: hidden;
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -2px rgba(0,0,0,.05) !important;
}


