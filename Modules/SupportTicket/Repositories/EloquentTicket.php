<?php

namespace Modules\SupportTicket\Repositories;


use App\Support\Enum\Perms;
use Modules\SupportTicket\app\Models\SupportTicket;

class EloquentTicket implements TicketRepository
{
    protected $baseQuery;

    public function getTicketForUser()
    {

        $table = "support_ticket";

        $user = auth()->user();

        $baseQuery = SupportTicket::query();

        $request = request();
        $filters = $request->only(['area', 'province', 'district', 'ward', 'branch', 'agency', 'pos']);

        $baseQuery = $baseQuery->where(function ($query) use ($filters) {
            $query->whereHas('pos', function ($query) use ($filters) {
                foreach ($filters as $key => $value) {
                    if (!empty($value)) {
                        if (is_array($value)) {
                            $query->whereIn("{$key}Code", $value);
                        } else {
                            $query->where("{$key}Code", $value);
                        }
                    }
                }
            })->orWhereDoesntHave('pos');
        });


        if ($user->hasPermission(Perms::BRANCH_MANAGER)) {
            return $baseQuery;
        } elseif ($user->hasPermission(Perms::BRANCH_OWNER) && $user->branch) {
            $baseQuery->whereHas('pos', function ($query) use ($user) {
                $query->where('branchCode', $user->branch->code);
            });
            return $baseQuery;
        } elseif ($user->hasPermission(Perms::POS_MANAGER)) {

            $baseQuery->whereHas('pos', function ($query) use ($user) {
                $query->where('staff_id', $user->id);
            })->whereHas('category', function ($query) use ($user) {
                $query->whereHas('roles', function ($query) use ($user) {
                    $query->where('assignable_id', $user->role_id);
                });
            });
            return $baseQuery;
        }

        return $baseQuery->where("{$table}.user_id", $user->id);
    }

    public function applyFilter($query)
    {
        $request = request();
        $filters = $request->only(['area', 'province', 'district', 'ward', 'branch', 'agency', 'pos']);

        foreach ($filters as $key => $value) {
            if (!empty($value)) {
                if ($key === 'province') {
                    $query->where('provinceCode', $value);
                } elseif ($key === 'district') {
                    if (is_array($value)) {
                        $query->whereIn('districtCode', $value);
                    } else {
                        $query->where('districtCode', $value);
                    }
                } elseif ($key === 'ward') {
                    $query->where('wardCode', $value);
                } elseif ($key === 'branch') {
                    $query->where('branchCode', $value);
                } elseif ($key === 'agency') {
                    if (is_array($value)) {
                        $query->whereIn('agencyCode', $value);
                    } else {
                        $query->where('agencyCode', $value);
                    }
                } elseif ($key === 'pos') {
                    $query->where('posCode', $value);
                } else {
                    $query->where($key, $value);
                }
            }
        }
        return $query;
    }


public function countTicket($user = null)
{
    $user = $user ?? auth()->user();

    // Base query lấy ticket theo phân quyền
    $query = SupportTicket::query();

    // Áp dụng logic phân quyền giống phần get list
    if ($user->hasPermission(Perms::BRANCH_MANAGER)) {
        if ($user->branch) {
            $query->whereHas('pos', function ($q) use ($user) {
                $q->where('branchCode', $user->branch->code);
            });
        }
    } elseif ($user->hasPermission(Perms::BRANCH_OWNER)) {
        if ($user->branch) {
            $query->whereHas('pos', function ($q) use ($user) {
                $q->where('branchCode', $user->branch->code);
            });
        }
        $query->whereHas('category.roles', function ($q) use ($user) {
            $q->where('roles.id', $user->role->id);
        });
    } elseif ($user->hasPermission(Perms::POS_MANAGER)) {
        $query->whereHas('pos', function ($q) use ($user) {
            $q->where('staff_id', $user->id);
        })->whereHas('category.roles', function ($q) use ($user) {
            $q->where('roles.id', $user->role->id);
        });
    } else {
        $query->whereHas('pos', function ($q) use ($user) {
            $q->where('staff_id', $user->id);
        })->whereHas('category.roles', function ($q) use ($user) {
            $q->where('roles.id', $user->role->id);
        });
    }

    // Apply filter phụ nếu có
    $query = $this->applyFilter($query);

    // Đếm số lượng theo status
    $pending   = (clone $query)->where('status', SupportTicket::STATUS_PENDING)->count();
    $inprocess = (clone $query)->where('status', SupportTicket::STATUS_INPROCESS)->count();
    $done      = (clone $query)->where('status', SupportTicket::STATUS_DONE)->count();
    $cancel    = (clone $query)->where('status', SupportTicket::STATUS_CANCEL)->count();
    $total     = SupportTicket::where('user_id', $user->id)->count();
    $support   = (clone $query)->count(); // Tổng tất cả

    return [
        'total'     => $total,
        'support'   => $support,
        'pending'   => $pending,
        'inprocess' => $inprocess,
        'done'      => $done,
        'cancel'    => $cancel,
    ];
}


    /**
     * Tổng ticket của pos cho nhân viên hiện tại
     */
    public function countTicketForStaff($user = null)
    {
        return $this->countTicket($user);
    }
    public function searchAndPaginateTicketsByUser($request, $pageSize)
    {
        $searchTerm = $request->input('search', '');
        $pageSize = $request->input('pageSize', $pageSize);
        $sort = $request->input('sort', 'newest');

        $query = SupportTicket::where('user_id', auth()->user()->id);

        if (!empty($searchTerm)) {
            $query->where(function ($q) use ($searchTerm) {
                $q->whereHas('category', function ($q) use ($searchTerm) {
                    $q->where('name', 'ilike', '%' . $searchTerm . '%');
                });
            });
        }
        if (!empty($sort)) {
            if ($sort == 'newest') {
                $query->orderBy('created_at', 'desc');
            } elseif ($sort == 'oldest') {
                $query->orderBy('created_at', 'asc');
            }
        }
        return $query->paginate($pageSize)->withQueryString();
    }

    private function convertStatusStringToInt($status)
    {
        $statusValues = [
            'pending' => SupportTicket::STATUS_PENDING,
            'inprocess' => SupportTicket::STATUS_INPROCESS,
            'done' => SupportTicket::STATUS_DONE,
            'cancel' => SupportTicket::STATUS_CANCEL,
            'support' => SupportTicket::STATUS_SUPPORT,
        ];

        return $statusValues[$status] ?? null;
    }
    public function searchAndPaginateWithStatus($request, $status, $pageSize)
    {

        $status = $this->convertStatusStringToInt($status);
        $searchTerm = $request->input('search', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('perPage', $pageSize);
        $sort = $request->input('sort', 'newest');

        $user_id = $request->has('userId') ? $request->userId : auth()->id();
        $authUser = auth()->user();

        $query = SupportTicket::query();

        // Luôn filter theo status nếu status khác STATUS_SUPPORT
        if ($status != SupportTicket::STATUS_SUPPORT) {
            $query->where('status', $status);
        }

        // Filter theo quyền
        if ($authUser->hasPermission(Perms::BRANCH_MANAGER)) {
            if ($status == SupportTicket::STATUS_SUPPORT) {
                if ($authUser->branch) {
                    $query->whereHas('pos', function ($q) use ($authUser) {
                        $q->where('branchCode', $authUser->branch->code);
                    });
                }
            }
        } elseif ($authUser->hasPermission(Perms::BRANCH_OWNER)) {
            if ($authUser->branch) {
                $query->whereHas('pos', function ($q) use ($authUser) {
                    $q->where('branchCode', $authUser->branch->code);
                });
            }
                $query->whereHas('category.roles', function ($q) use ($authUser) {
                    $q->where('roles.id', $authUser->role->id);
                });

        } elseif ($authUser->hasPermission(Perms::POS_MANAGER)) {
            $query->whereHas('pos', function ($q) use ($user_id) {
                $q->where('staff_id', $user_id);
            })->whereHas('category.roles', function ($q) use ($authUser) {
                $q->where('roles.id', $authUser->role->id);
            });
        } else {
            $query->whereHas('pos', function ($q) use ($user_id) {
                $q->where('staff_id', $user_id);
            })->whereHas('category.roles', function ($q) use ($authUser) {
                $q->where('roles.id', $authUser->role->id);
            });
        }


        if (!empty($searchTerm)) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'ILIKE', "%{$searchTerm}%")
                    ->orWhere('status', 'ILIKE', "%{$searchTerm}%");
            });
        }

        if (!empty($sort)) {
            $sortColumn = 'created_at';
            $sortDirection = $sort === 'newest' ? 'desc' : 'asc';
            $query->orderBy($sortColumn, $sortDirection);
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}
