<?php

namespace Modules\SupportTicket\app\Observers;

use App\Notifications\MessagesNotification;
use App\User;
use App\Support\Enum\Perms;
use App\Repositories\Pos\PosRepository;
use App\Pos;
use Modules\SupportTicket\app\Models\SupportTicketComment;

class SupportTicketCommentObserver
{
    protected $posRepository;

    public function __construct(PosRepository $posRepository)
    {
        $this->posRepository = $posRepository;
    }

    /**
     * Handle the SupportTicketComment "created" event.
     */
    public function created(SupportTicketComment $comment): void
    {
        // Lấy ticket hiện tại với creator và category
        $ticket = $comment->supportTicket;

        // Collect tất cả users cần nhận thông báo
        $usersToNotify = collect();

        // 1. Thêm người tạo ticket
        if ($ticket->creator && $ticket->creator->id != auth()->id()) {
            $usersToNotify->push($ticket->creator);
        }

        // 2. <PERSON><PERSON><PERSON> danh sách users có role được assign cho category của ticket
        $roleIds = $ticket->category->roles->pluck('id');
        $usersWithAssignedRoles = User::whereHas('role', function($query) use ($roleIds) {
            $query->whereIn('id', $roleIds);
        })->where('id', '!=', auth()->id())->get();

        // 3. Filter các users có quyền truy cập POS
        foreach ($usersWithAssignedRoles as $user) {
            $posQuery = $this->posRepository->getAccessiblePosQuery($user)
                ->where('posStatus', Pos::STATUS_ACTIVE)
                ->where('posCode', $ticket->assigner_id);

            if ($posQuery->exists()) {
                $usersToNotify->push($user);
            }
        }

        // 4. Thêm những người đã comment trước đó
        $commenters = SupportTicketComment::where('support_ticket_id', $ticket->id)
            ->where('user_id', '!=', auth()->id())
            ->with('user')
            ->get()
            ->pluck('user');

        $usersToNotify = $usersToNotify->concat($commenters)->unique('id');

        // Gửi thông báo cho tất cả users
        foreach ($usersToNotify as $user) {
            $user->notify(new MessagesNotification([
                'line' => 'Bạn có một thông báo mới về yêu cầu hỗ trợ.',
                'actionText' => 'Xem Ngay',
                'actionUrl' => route('ticket.reply.index', $comment->support_ticket_id),
                'title' => $comment->user->username . ' đã bình luận ',
                'body' => $comment->comment,
            ]));
        }
    }
}
