<?php

namespace Modules\SupportTicket\app\Observers;

use App\Notifications\MessagesNotification;
use App\User;
use App\Pos;
use Modules\SupportTicket\app\Models\SupportTicket;
use Illuminate\Support\Facades\Notification;
use App\Repositories\Pos\PosRepository;

class SupportTicketObserver
{
    protected $posRepository;

    public function __construct(PosRepository $posRepository)
    {
        $this->posRepository = $posRepository;
    }

    /**
     * Handle the SupportTicket "created" event.
     */
    public function created(SupportTicket $ticket): void
    {
        // Load category với notifications
        $category = $ticket->category;

        // Kiểm tra xem category có cho phép gửi thông báo không
        if (!$category || !$category->enable_notification) {
            return;
        }

        // L<PERSON>y danh sách roles được cấu hình nhận thông báo
        $roleIds = $ticket->category->roles->pluck('id');

        // L<PERSON>y tất cả users thuộc các roles được cấu hình nhận thông báo
        $allUsersWithRoles = User::whereHas('role', function ($query) use ($roleIds) {
            $query->whereIn('id', $roleIds);
        })->where('id', '!=', auth()->id())->get();

        // Filter users có quyền truy cập POS
        $users = collect();
        foreach ($allUsersWithRoles as $user) {
            $posQuery = $this->posRepository->getAccessiblePosQuery($user)
                ->where('posStatus', Pos::STATUS_ACTIVE)
                ->where('posCode', $ticket->assigner_id);

            if ($posQuery->exists()) {
                $users->push($user);
            }
        }

        // Gửi thông báo cho từng user
        Notification::send($users, new MessagesNotification([
            'line' => 'Có một yêu cầu hỗ trợ mới cần được xử lý.',
            'actionText' => 'Xem Ngay',
            'actionUrl' => route('ticket.reply.index', $ticket->id),
            'title' => 'Ticket mới từ ' . auth()->user()->username,
            'body' => $ticket->body,
        ]));
    }
}
