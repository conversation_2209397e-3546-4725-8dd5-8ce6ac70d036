<?php

namespace Modules\SupportTicket\app\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Notifications\MessagesNotification;
use App\Pos;
use App\User;
use Illuminate\Http\Request;
use Modules\SupportTicket\app\Models\SupportTicket;
use Modules\SupportTicket\app\Models\SupportTicketComment;

class SupportTicketCommentController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:feedback.view');
    }

    public function store(Request $request)
    {
        $comment = new SupportTicketComment;
        $comment->comment = strip_tags($request->input('comment'));
        $comment->support_ticket_id = $request->input('support_ticket_id');
        $comment->user_id = auth()->id();
        $comment->files = $request->input('files') ? explode(',', $request->input('files')) : [];

        $comment->save();

        return response()->json(['data' => [
            'comment' => $comment->comment,
            'created_at' => $comment->created_at->diffForHumans(),
            'user' => [
                'avatar' => $comment->user->present()->avatar,
                'username' => $comment->user->username,
            ],
        ]]);
    }
}
