<?php

namespace Modules\SupportTicket\app\Console\Commands;

use Illuminate\Console\Command;
use Modules\SupportTicket\app\Models\SupportTicket;
use Modules\SupportTicket\app\Models\CategoryTicket;
use App\Notifications\MessagesNotification;
use App\Repositories\Pos\PosRepository;
use App\Pos;
use Carbon\Carbon;

class NotifyPendingTickets extends Command
{
    protected $signature = 'support-ticket:notify-pending';
    protected $description = 'Send notifications for pending tickets that have exceeded the notification delay time';

    protected $posRepository;

    public function __construct(PosRepository $posRepository)
    {
        parent::__construct();
        $this->posRepository = $posRepository;
    }

    public function handle()
    {
        // Get notification delay in seconds
        $delayInSeconds = CategoryTicket::getNotificationDelayInSeconds();

        // Get pending tickets that have exceeded the delay time
        $pendingTickets = SupportTicket::where('status', SupportTicket::STATUS_PENDING)
            ->where('created_at', '<=', Carbon::now()->subSeconds($delayInSeconds))
            ->with(['category.notifications', 'creator'])
            ->get();

        foreach ($pendingTickets as $ticket) {
            // Skip if category doesn't have notification enabled
            if (!$ticket->category->enable_notification) {
                continue;
            }

            // Get users from notification settings
            $users = $ticket->category->notifications()
                ->with('users')
                ->get()
                ->pluck('users')
                ->flatten()
                ->unique('id');

            // Filter users based on POS access
            $usersToNotify = collect();
            foreach ($users as $user) {
                $posQuery = $this->posRepository->getAccessiblePosQuery($user)
                    ->where('posStatus', Pos::STATUS_ACTIVE)
                    ->where('posCode', $ticket->assigner_id);

                if ($posQuery->exists()) {
                    $usersToNotify->push($user);
                }
            }

            // Prepare notification data
            $notificationData = [
                'line' => 'Yêu cầu hỗ trợ đang chờ xử lý quá thời hạn.',
                'actionText' => 'Xem Ngay',
                'actionUrl' => route('ticket.reply.index', $ticket->id),
                'title' => 'Ticket #' . $ticket->id . ' cần được xử lý',
                'body' => $ticket->body,
            ];

            // Send notification to each user with POS access
            foreach ($usersToNotify as $user) {
                $user->notify(new MessagesNotification($notificationData));
            }
        }

        $this->info('Notifications sent for ' . $pendingTickets->count() . ' pending tickets');
    }
}
