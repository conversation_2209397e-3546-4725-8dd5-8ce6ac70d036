<?php

namespace Modules\SupportTicket\app\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Console\Scheduling\Schedule;
use Modules\SupportTicket\app\Models\SupportTicket;
use Modules\SupportTicket\app\Models\SupportTicketComment;
use Modules\SupportTicket\app\Observers\SupportTicketObserver;
use Modules\SupportTicket\app\Observers\SupportTicketCommentObserver;
use Modules\SupportTicket\Repositories\EloquentTicket;
use Modules\SupportTicket\Repositories\TicketRepository;

class SupportTicketServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'SupportTicket';

    protected string $moduleNameLower = 'supportticket';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerObservers();
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'database/migrations'));
        //        View::composer(
        //            ['supportticket::inbox.reply' ,'partials.sidebar.sidebar','layouts.app','partials.header.menu.menu'],'Modules\SupportTicket\app\ViewComposers\TicketComposer',
        //
        //        );

        View::composer(
            ['partials.sidebar.sidebar', 'layouts.app'],
            'Modules\SupportTicket\app\ViewComposers\TicketComposer',
        );
    }

    /**
     * Register observers.
     */
    protected function registerObservers(): void
    {
        SupportTicket::observe(SupportTicketObserver::class);
        SupportTicketComment::observe(SupportTicketCommentObserver::class);
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(RouteServiceProvider::class);
        $this->app->bind(TicketRepository::class, EloquentTicket::class);
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        // $this->commands([]);
        $this->commands([
            \Modules\SupportTicket\app\Console\Commands\NotifyPendingTickets::class
        ]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
        $this->app->booted(function () {
            $schedule = $this->app->make(Schedule::class);
            $schedule->command('support-ticket:notify-pending')->dailyAt('07:00');
        });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'lang'), $this->moduleNameLower);
            $this->loadJsonTranslationsFrom(module_path($this->moduleName, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $this->publishes([module_path($this->moduleName, 'config/config.php') => config_path($this->moduleNameLower . '.php')], 'config');
        $this->mergeConfigFrom(module_path($this->moduleName, 'config/config.php'), $this->moduleNameLower);
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);
        $sourcePath = module_path($this->moduleName, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->moduleNameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);

        $componentNamespace = str_replace('/', '\\', config('modules.namespace') . '\\' . $this->moduleName . '\\' . config('modules.paths.generator.component-class.path'));
        Blade::componentNamespace($componentNamespace, $this->moduleNameLower);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }

        return $paths;
    }
}
