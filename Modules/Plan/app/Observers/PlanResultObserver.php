<?php

namespace Modules\Plan\app\Observers;

use App\Notifications\PlanNotification;
use App\Repositories\Pos\PosRepository;
use App\Role;
use App\User;
use Modules\Plan\app\Models\PlanResult;

class PlanResultObserver
{
    protected PosRepository $posRepository;

    function __construct(PosRepository $posRepository)
    {
        $this->posRepository = $posRepository;
    }
    /**
     * Handle the PlanResult "created" event.
     */
    public function created(PlanResult $planResult): void
    {
        $warnStatuses = [
            PlanResult::WARN_WRONG_CHECKOUT_LOCATION,
            PlanResult::WARN_CHECKED_OUT_EARLY,
            PlanResult::WARN_CHECKED_IN_EARLY,
            PlanResult::WARN_CHECKED_IN_LATE,
        ];
        // Check if this is an unplanned visit
        if (isset($planResult->data['warn_status']) && array_intersect($warnStatuses, $planResult->data['warn_status'])) {
            // Get all users with ROLE_CORPORATION
            $corporateUsers = User::whereHas('role', function ($query) {
                $query->where('name', Role::ROLE_CORPORATION);
            })->get();

            $pos = $planResult->pos;
            $staff = $planResult->staff;

            // Only send notifications to users who have access to this POS
            foreach ($corporateUsers as $user) {

                $accessiblePosCodes = $this->posRepository->getAccessiblePosQuery($user)
                    ->pluck('posCode')
                    ->toArray();

                // Skip users who don't have access to any POS or this specific POS
                if (empty($accessiblePosCodes) || !in_array($planResult->posCode, $accessiblePosCodes)) {
                    continue;
                }

                $notificationData = [
                    'title' => 'Ghé thăm sai kế hoạch',
                    'body' => sprintf(
                        '%s đã ghé thăm điểm bán hàng %s sai kế hoạch "%s" vào lúc %s',
                        $staff->present()->name,
                        $pos->posCode,
                        lcfirst(PlanResult::WARN_CHECKIN_STATUS[$planResult->data['warn_status']]),
                        $planResult->created_at->format('H:i d/m/Y')
                    ),
                    'screen' => 'VisitPlanScreen',
                    'type' => 'warning'
                ];

                $user->notify(new PlanNotification($notificationData));
            }
        }
    }

    /**
     * Handle the PlanResult "updated" event.
     */
    public function updated(PlanResult $planResult): void
    {
        //
    }

    /**
     * Handle the PlanResult "deleted" event.
     */
    public function deleted(PlanResult $planResult): void
    {
        //
    }

    /**
     * Handle the PlanResult "restored" event.
     */
    public function restored(PlanResult $planResult): void
    {
        //
    }

    /**
     * Handle the PlanResult "force deleted" event.
     */
    public function forceDeleted(PlanResult $planResult): void
    {
        //
    }
}
