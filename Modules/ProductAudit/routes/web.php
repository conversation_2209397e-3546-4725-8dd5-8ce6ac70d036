<?php

use Illuminate\Support\Facades\Route;
use Modules\ProductAudit\app\Http\Controllers\ProductAuditController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth', 'verified'])->prefix('product-audit')->name('product-audit.')->group(function () {
    Route::get('spa/{any?}', function ($any = '') {
        return view('productaudit::index');
    })->where('any', '.*')->name('master');

    Route::get('dashboard/{id}', [ProductAuditController::class, 'show'])->name('dashboard');

    Route::get('get-data/{id}', [ProductAuditController::class, 'getData'])->name('getData');

    Route::resource('product-audit', ProductAuditController::class);

    Route::get('start-product-audit/{id}', [ProductAuditController::class, 'startProductAudit'])->name('startProductAudit');

});
