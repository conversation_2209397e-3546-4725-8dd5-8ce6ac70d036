
<tr>
    <td>
        <div class="form-check form-check-sm form-check-custom form-check-solid">
            @if ($product_audit->id != Auth::id())
                <input name="selected_items[]" class="form-check-input" type="checkbox" value="{{ $product_audit->id }}"/>
            @endif
        </div>
    </td>
    <td class="d-flex align-items-center">
        <!--begin:: Avatar -->
        <div class="symbol symbol-circle symbol-50px overflow-hidden me-3">
            <a class="view-user" href="{{ route('users.show', $product_audit) }}">
                <div class="symbol-label">
                    {{--                    <img src="{{ $product_audit->present()->avatar }}" alt="{{ $product_audit->present()->name }}" class="w-100" />--}}
                </div>
            </a>
        </div>
        <!--end::Avatar-->
        <!--begin::User details-->
        <div class="d-flex flex-column">
            <a class="view-user" href="{{ route('users.show', $product_audit) }}"
               class="text-gray-800 text-hover-primary mb-1">{{ $product_audit->name }}</a>
            {{--            <span>{{ $product_audit->email }}</span>--}}
        </div>
        <!--begin::User details-->
    </td>
    <td>
        {{ $product_audit->name }}
    </td>
    <td>
        {{ $product_audit->name}}
    </td>
    {{-- <td>
        <div class="badge badge-light fw-bold">{{ $product_audit->last_login }}</div>
    </td> --}}
    {{-- <td>
        <div class="badge badge-light-success fw-bold">{{ __('Enabled') }} </div>
    </td> --}}
    <td>
        <span class="badge badge-lg badge-{{ $product_audit->labelClass }}">
            {{ __($product_audit->status) }}
        </span>
    </td>
    <td>{{ $product_audit->created_at->format(config('app.date_format')) }}</td>
    <td class="text-end">
        <a href="#" class="btn btn-light btn-active-light-primary btn-flex btn-center btn-sm"
           data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="ki-duotone ki-setting-3 fs-3">
                <span class="path1"></span>
                <span class="path2"></span>
                <span class="path3"></span>
                <span class="path4"></span>
                <span class="path5"></span>
            </i>
            <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
        <!--begin::Menu-->
        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
             data-kt-menu="true">
            <!--begin::Menu item-->
            <div class="menu-item px-3">
                <a id="view-user-{{$product_audit->id}}" href="{{ route('product-audit.product-audit.show', $product_audit->id) }}"
                   class="menu-link px-3">{{ __('View') }}</a>
            </div>
            <!--end::Menu item-->

            <!--begin::Menu item-->
            <div class="menu-item px-3">
                <a id="view-user-{{$product_audit->id}}" href="{{ route('survey.startSurvey', $product_audit->id) }}"
                   class="menu-link px-3">Làm Kiểm kê TTB</a>
            </div>
            <!--end::Menu item-->
            <!--begin::Menu item-->
            {{-- <div class="menu-item px-3">
                <a href="{{ route('users.edit', $product_audit) }}" class="menu-link px-3">{{ __('Edit') }}</a>
            </div> --}}
            <div class="menu-item px-3">
                <a href="{{route('product-audit.product-audit.edit',$product_audit->id)}}"
                   class="menu-link px-3">{{ __('Edit') }}</a>
            </div>
            <!--end::Menu item-->

            <!--begin::Menu item-->
            <div class="menu-item px-3">
                <form method="POST" action="{{ route('product-audit.product-audit.destroy', $product_audit) }}"
                      id="delete-item-form-{{$product_audit->id}}">
                    @csrf
                    @method('DELETE')
                    <a href="#"
                       class="menu-link px-3"
                       id="delete-survey-{{$product_audit->id}}"
                       data-name="{{$product_audit->name}}"
                       data-kt-table-action="delete_row">{{ __('Delete') }}</a>
                </form>
            </div>
            <!--end::Menu item-->
        </div>
        <!--end::Menu-->
    </td>
</tr>
