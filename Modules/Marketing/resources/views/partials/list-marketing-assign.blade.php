@pushonce('style-component')
    {!! HTML::style(asset('assets/plugins/custom/datatables/datatables.bundle.css')) !!}
@endpushonce
@use('\Modules\Marketing\app\Models\Marketing')
@use('\App\Role')

<div class="card">
    <!--begin::Card header-->
    <form action="" method="GET">
        <div class="card-header border-0 pt-6">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                {{-- <div class="d-flex align-items-center position-relative my-1">
                    <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <input type="text" name="search" value="{{ request('search')}}"
                        class="form-control form-control-solid w-250px ps-13 w-ms-100" placeholder="Nhập tìm kiếm" />
                </div> --}}

                <!--end::Search-->
            </div>
            <!--begin::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar">
                <!--begin::Toolbar-->
                @if ($disabled)
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#kt_modal_assign_pos">Điểm bán được phân giao ({{ $marketing->pos->count() }})
                        <span class="ms-2" data-bs-toggle="tooltip" title="Phân giao điểm bán hàng cho marketing">
                            <i class="ki-duotone ki-information fs-7">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </i>
                        </span>
                    </button>


                    {{-- <button type="button" class="btn btn-primary me-3" data-bs-toggle="modal"
                        data-bs-target="#kt_modal_assign_marketing_items">Phân giao vật phẩm marketing
                        <span class="ms-2" data-bs-toggle="tooltip"
                            title="Phân giao vật phẩm marketing cho chương trình marketing này">
                            <i class="ki-duotone ki-information fs-7">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </i>
                        </span>
                    </button> --}}
                @endif

                <div class="d-flex justify-content-end" data-kt-pos-table-toolbar="base">

                    {{--   <!--begin::Filter-->
                       <button type="button" class="btn btn-light-primary " data-kt-menu-trigger="click"
                               data-kt-menu-placement="bottom-end">
                           <i class="ki-duotone ki-filter fs-2">
                               <span class="path1"></span>
                               <span class="path2"></span>
                           </i>{{ __('Filter') }}
                       </button>
                       <div class="menu menu-sub menu-sub-dropdown" data-kt-menu="true">
                           <div class="px-7 py-5">
                               <div class="fs-5 text-gray-900 fw-bold">{{ __('Filter Options') }}</div>
                           </div>
                           <div class="separator border-gray-200"></div>
                           <div class="px-7 py-5" data-kt-pos-table-filter="form">
                               <div class="row">
                                   <div class="col">
                                       <div class="mb-10 w-300px w-md-325px">
                                           @include('partials.filters.location-filter',['spa'=>true,'label' => 'Lọc theo Tỉnh/Thành phố','hiddenArea'=>true])
                                       </div>

                                   </div>
                                   <div class="col">
                                       <div class="w-300px w-md-325px">

                                           <!--begin::Input group-->
                                           @include('partials.filters.structure-filter',['spa'=>true,'label' => 'Lọc theo Chi nhánh/đại lý','hiddenPos'=>true])
                                           <!--end::Input group-->
                                       </div>

                                   </div>
                               </div>

                               <!--begin::Actions-->
                               <div class="d-flex justify-content-end">
                                   <a href="{{ route('branch.assignPosTobranch',$branch->id) }}" type="reset"
                                      class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6">{{ __('Reset') }}</a>
                                   <button type="submit"
                                           class="btn btn-primary fw-semibold px-6">{{ __('Apply') }}</button>
                               </div>
                               <!--end::Actions-->
                           </div>


                           <!--end::Content-->
                       </div>
                       <!--end::Menu 1-->
                       <!--end::Filter-->
   --}}

                </div>
            </div>
            <!--end::Card toolbar-->
        </div>
    </form>
    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body py-4 table-content min-h-500px table-responsive">
        <!--begin::Table-->
        <table class="table align-middle table-row-dashed fs-6 gs-5" id="kt_table_province">
            <thead>
                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                    <th class="min-w-150px">Tên</th>
                    <th class="min-w-100px">CODE</th>
                    <th class="min-w-100px">Đơn vị</th>
                    <th class="w-150px text-center">SL đề xuất</th>
                    <th class="w-150px text-center">SL thực nhận</th>
                    @if ($disabled)
                        <th class="text-end min-w-125px">{{ __('Actions') }}</th>
                    @endif

                </tr>
            </thead>
            <tbody class="text-gray-600 fw-semibold " id="kt_tbody_pos">
                @foreach ($marketing->assignments as $pos)
                    <tr @class([
                        'fw-bold text-center pos-assignment',
                        'table-info' => count($pos->items),
                        'table-warning' => count($pos->items) <= 0,
                    ])>
                        <td colspan="{{ $disabled ? '5' : '6' }}" class="">
                            Điểm bán: <a href="{{ route('pos.show', $pos->pos?->posCode) }}" class="text-info"> {{ $pos->pos?->posCode }} -
                                {{ $pos->pos?->address }}</a> Trạng thái: <span
                                class="badge {{ Modules\Marketing\app\Models\Marketing::STATUS_CLASS[$pos->status] }} text-white">{{ Modules\Marketing\app\Models\Marketing::STATUS_LABELS[$pos->status] }}</span>
                        </td>
                        @if ($disabled)
                            <td class="text-end">
                                <a href="#"
                                    class="btn btn-light btn-active-light-primary btn-flex btn-center btn-sm"
                                    data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                    <i class="ki-duotone ki-setting-3 fs-3">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                        <span class="path5"></span>
                                    </i>
                                    <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-auto m-w-225px py-4"
                                    data-kt-menu="true">

                                    @permission('marketing.update')
                                        <div class="menu-item px-3">
                                            <a href="#" data-bs-toggle="modal"
                                                data-pos-code="{{ $pos->pos?->posCode }}"
                                                data-exclude-items="{{ $pos->items->pluck('item_id')->toJson() }}"
                                                data-bs-target="#kt_modal_assign_marketing_items" class="menu-link px-3">Vật
                                                phẩm marketing</a>
                                        </div>
                                        <!--begin::Menu item-->
                                        <div class="menu-item px-3">
                                            <form method="POST"
                                                action="{{ route('marketing.removeAssignPos', $marketing->id) }}"
                                                id="delete-item-form-{{ $marketing->id }}">
                                                @csrf
                                                <input type="hidden" name="pos_ids[]" value="{{ $pos->pos?->posCode }}">
                                                <a href="#" class="menu-link px-3" data-method-cb="reload"
                                                    id="delete-pos-{{ $marketing->id }}"
                                                    data-name="{{ $pos->pos?->posCode }}"
                                                    data-kt-table-action="delete_row">Xoá điểm bán</a>
                                            </form>
                                        </div>
                                    @endpermission
                                </div>
                            </td>
                        @endif
                    </tr>
                    @foreach ($pos->items as $item)
                        <tr>
                            <td>
                                <div class="d-flex">
                                    <!--begin::Thumbnail-->
                                    <a href="#" class="symbol symbol-35px">
                                        <span class="symbol-label"
                                            style="background-image:url('{{ asset($item->item?->photo) }}');"></span>
                                    </a>
                                    <!--end::Thumbnail-->
                                    <div class="ms-5 d-flex align-items-center">
                                        <!--begin::Title-->
                                        <a href="#" class="text-gray-800 fs-5 fw-bold mb-1"
                                            data-kt-ecommerce-category-filter="category_name">{{ $item->item?->name }}</a>
                                    </div>
                                </div>
                            </td>

                            <td>{{ $item->item?->code }}</td>
                            <td>{{ $item->item?->unit?->unit_name }}</td>
                            <td>
                                @if ($disabled)
                                    <div class="input-group mb-5">
                                        <input type="number" placeholder="Số lượng" min="1"
                                            value="{{ $item->proposed_qty }}"
                                            data-marketing_item_id="{{ $item->id }}" data-type_qty="proposed_qty"
                                            class="form-control min-visit-input form-control-sm"
                                            aria-describedby="basic-addon2" disabled />

                                        <span class="input-group-text edit-icon cursor-pointer" data-bs-toggle="tooltip"
                                            title="Điều chỉnh số lượng"
                                            style="border-top-right-radius: 2.1rem;border-bottom-right-radius: 2.1rem;">
                                            <i class="ki-duotone ki-pencil">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span class="input-group-text save-icon d-none cursor-pointer"
                                            data-bs-toggle="tooltip" title="Lưu">
                                            <i class="fa-solid fa-floppy-disk text-success"></i>
                                        </span>
                                    </div>
                                @else
                                    <div class="text-center"> {{ $item->proposed_qty }}</div>
                                @endif
                            </td>
                            <td>
                                @if (false)
                                    <div class="input-group mb-5">
                                        <input type="number" placeholder="Số lượng" value="{{ $item->actual_qty }}"
                                            min="1" data-marketing_item_id="{{ $item->id }}"
                                            data-type_qty="actual_qty"
                                            class="form-control min-visit-input form-control-sm"
                                            aria-describedby="basic-addon2" disabled />

                                        <span class="input-group-text edit-icon cursor-pointer"
                                            data-bs-toggle="tooltip" title="Chỉnh sửa số lượng"
                                            style="border-top-right-radius: 2.1rem;border-bottom-right-radius: 2.1rem;">
                                            <i class="ki-duotone ki-pencil">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span class="input-group-text save-icon d-none cursor-pointer"
                                            data-bs-toggle="tooltip" title="Lưu">
                                            <i class="ki-duotone ki-save-deposit text-success">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                                <span class="path4"></span>
                                            </i>
                                        </span>
                                    </div>
                                @else
                                    <div class="text-center">{{ $item->actual_qty }}</div>
                                @endif
                            </td>
                            @if ($disabled)
                                <td class="text-end">
                                    <form method="POST"
                                        action="{{ route('marketing.removeMarketingItemsAssign', ['marketing_id' => $marketing->id, 'marketing_item_id' => $item->id]) }}"
                                        id="delete-item-form-{{ $item->id }}">
                                        @csrf
                                        <input type="hidden" name="pos_code" value="{{ $pos->pos?->posCode }}">
                                        <input type="hidden" name="marketing_item_ids[]"
                                            value="{{ $item->id }}">

                                        <a href="#" class="btn btn-color-muted btn-active-color-danger"
                                            data-bs-toggle="tooltip" data-bs-placement="top"
                                            title="Xóa {{ $item->item?->name }} khỏi điểm bán {{ $pos->pos?->posCode }}"
                                            data-method-cb="reload" id="delete-pos-assign-{{ $item->id }}"
                                            data-name="{{ $item->item?->name }} khỏi điểm bán {{ $pos->pos?->posCode }}"
                                            data-kt-table-action="delete_row">
                                            <i class="ki-duotone ki-trash-square fs-1">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                                <span class="path4"></span>
                                            </i>
                                        </a>

                                    </form>
                                </td>
                            @endif
                        </tr>
                    @endforeach
                @endforeach

            </tbody>
        </table>
    </div>


    <!--end::Card body-->
</div>

@push('section-modal')
    <div class="modal fade" id="kt_modal_assign_marketing_items" tabindex="-1" aria-hidden="true">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-lg">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header pb-0 border-0 justify-content-between">
                    <h2 class="modal-title">Phân giao vật phẩm marketing cho điểm bán <span class="pos_code"></span> </h2>
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <i class="ki-duotone ki-cross fs-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                </div>

                <div class="modal-body ">
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade" id="kt_modal_assign_pos" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header pb-0 border-0 justify-content-between">
                    <h2 class="modal-title">Phân giao điểm bán</h2>
                    <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                        <i class="ki-duotone ki-cross fs-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row mb-8" id="modal-create-marketing-table-filter">
                        <div class="col-3">
                            <div class="dataTables_filter">
                                <input type="text" aria-controls="kt_model_table_datatable_pos" class="form-control"
                                    placeholder="Nhập tìm kiếm..." />
                            </div>
                        </div>
                        <div class="col-3">
                            @include('partials.filters.select2.BranchSelect', [
                                'label' => false,
                                'dropdownParent' => '#kt_modal_assign_pos',
                            ])
                        </div>
                        <div class="col-3">
                            @include('partials.filters.select2.AgencySelect', [
                                'label' => false,
                                'dropdownParent' => '#kt_modal_assign_pos',
                                'disabled' => false,
                            ])
                        </div>
                        {{-- <div class="col-3">
                            @include('partials.filters.select2.StaffSelect', [
                                'label' => false,
                                'dropdownParent' => '#kt_modal_assign_pos',
                                'disabled' => false,
                            ])
                        </div> --}}
                    </div>

                    <table id="kt_model_table_datatable_pos" class="table align-middle table-row-dashed fs-6">
                        <thead>
                            <tr class="text-start text-gray-800 fw-bold fs-7 text-uppercase gs-0">
                                <th class="w-10px pe-2">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                        <input class="form-check-input form-check-input-pos" type="checkbox"
                                            data-kt-check="true"
                                            data-kt-check-target="#kt_model_table_datatable_pos .form-check-input-pos"
                                            value="" />
                                    </div>
                                </th>
                                <th>Pos ID</th>
                                <th class="min-w-150px">
                                    Ghé thăm
                                    <span data-bs-toggle="tooltip" data-bs-placement="top"
                                        title="Thời gian ghé thăm gần nhất">
                                        <i class="ki-duotone ki-information">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </span>
                                </th>
                                <th class="min-w-125px">
                                    Doanh thu
                                    <span data-bs-toggle="tooltip" data-bs-placement="top"
                                        title="Doanh thu {{ setting('pos_warn.no_revenue') }} ngày gần nhất">

                                        <i class="ki-duotone ki-information">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </span>
                                </th>
                                <th class="min-w-125px">Tỉnh/Thành phố</th>
                                <th class="min-w-175px">Đại lý</th>
                                <th class="min-w-200px">Địa chỉ</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-600 fw-bold"></tbody>
                    </table>
                </div>

                <div class="modal-footer py-2 d-none" data-kt-pos-table-modal-toolbar="selected">
                    <div class="fw-bold me-5">
                        <span class="me-2" data-kt-pos-table-modal-select="selected_count"></span>{{ __('Selected') }}
                    </div>
                    <button type="button" class="btn btn-info" data-kt-pos-table-modal-select="assign_selected">Phân
                        giao
                    </button>
                </div>
            </div>
        </div>
    </div>
@endpush

@push('script-component')
    <script src="{{ asset('assets/plugins/custom/datatables/datatables.bundle.js') }}"></script>
    <script>
        $(document).ready(function() {

            function convertQueryStringToObject(queryString) {
                var query = {};
                var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
                for (var i = 0; i < pairs.length; i++) {
                    var pair = pairs[i].split('=');
                    query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
                }
                return query;
            }

            $(document).on('click',
                '#kt_modal_assign_marketing_items .pagination a, #kt_modal_assign_marketing_items .reset',
                function(e) {
                    e.preventDefault();
                    var url = $(this).attr('href');
                    loadModalContent(url);
                });

            $(document).on('change', '#kt_modal_assign_marketing_items select[name="kt_pagination_table_pagesize"]',
                function(e) {
                    var newUrl = $(this).val();
                    loadModalContent(newUrl);
                });

            // Submit form bộ lọc
            $(document).on('submit', '#kt_modal_assign_marketing_items #filterForm', function(e) {
                e.preventDefault();

                var url = $(this).attr('action');
                var queryStringData = $(this).serialize();
                var data = convertQueryStringToObject(queryStringData);
                loadModalContent(url, data);
            });


            function loadModalContent(url, params = {}) {

                $('#kt_modal_assign_marketing_items .modal-body').html(`
                    <div class="overlay-layer card-rounded text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                </div>`);

                axios.get(url, {
                        params: params
                    })
                    .then(function(response) {
                        $('#kt_modal_assign_marketing_items .modal-body').html(response.data.html);
                        KTMenu.createInstances();
                        $('[name="branch"]').select2({});
                        $('[name="province"]').select2({});
                    })
                    .catch(function(error) {
                        console.error('Error loading content:', error);
                    });
            }

            $('#kt_modal_assign_marketing_items').on('show.bs.modal', function(event) {
                var button = $(event.relatedTarget);
                var posCode = button.data('pos-code');
                var excludeItems = button.data('exclude-items');

                $(this).find('.pos_code').text(posCode);

                loadModalContent("{{ route('marketing.getMarketingItemsAssignContent', $marketing) }}", {
                    'pos_code': posCode,
                    'exclude_items': excludeItems
                });
            });
        });
    </script>

    <script>
        "use strict";

        var KTPos = (function() {
            var table;
            var datatable;
            var toolbarBase;
            var toolbarSelected;
            var selectedCount;


            // Private functions
            var initPosTable = function() {
                datatable = $(table).DataTable({
                    info: false,
                    paging: false,
                    pageLength: -1,
                    order: [],
                    columnDefs: [{
                        orderable: false,
                        targets: 0
                    }, ],
                    scrollX: false,
                    autoWidth: false,
                    language: {
                        emptyTable
                    },
                });

                datatable.on("draw", function() {
                    toggleToolbars();
                });
            };


            // Toggle toolbars
            const toggleToolbars = () => {
                // Select refreshed checkbox DOM elements
                const allCheckboxes = table.querySelectorAll('tbody [type="checkbox"]');

                // Detect checkboxes state & count
                let checkedState = false;
                let count = 0;

                // Count checked boxes
                allCheckboxes.forEach((c) => {
                    if (c.checked) {
                        checkedState = true;
                        count++;
                    }
                });

                // Toggle toolbars
                if (checkedState) {
                    selectedCount.innerHTML = count;
                    toolbarBase.classList.add("d-none");
                    toolbarSelected.classList.remove("d-none");
                } else {
                    toolbarBase.classList.remove("d-none");
                    toolbarSelected.classList.add("d-none");
                }
            };

            return {
                // Public functions
                init: function() {
                    table = document.querySelector('#kt_table_province');
                    if (!table) {
                        return;
                    }
                    // initPosTable();
                },
            };
        })();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTPos.init();
        });
    </script>


    <script>
        $(document).ready(function() {
            $('.edit-icon').click(function() {
                $(this).siblings('.min-visit-input').prop('disabled', false).focus();
                $(this).addClass('d-none');
                $(this).siblings('.save-icon').removeClass('d-none');
            });

            $('.save-icon').click(function() {
                var input = $(this).siblings('.min-visit-input');
                var edit = $(this).siblings('.edit-icon');
                var save = $(this);
                var marketing_item_id = input.data('marketing_item_id');
                var type_qty = input.data('type_qty');
                var qty = input.val();

                $.ajax({
                    url: '{{ route('marketing.updateQty') }}',
                    type: 'POST',
                    data: {
                        'qty': qty,
                        'type_qty': type_qty,
                        'marketing_item_id': marketing_item_id,
                        '_token': KTApp.csrfToken(),
                    },
                    success: function({
                        message
                    }) {
                        KTApp.notify({
                            title: message
                        })
                        input.prop('disabled', true);

                        edit.removeClass('d-none');
                        save.addClass('d-none');
                    },
                    error: function(error) {
                        KTApp.notify({
                            title: "Đã xảy ra vui lòng thử lại sau",
                            icon: 'error'
                        })
                        console.error(error);
                    }
                });
            });

            // Optional: Handle outside click to cancel edit
            // $(document).click(function(event) {
            //     if (!$(event.target).closest('.input-group').length) {
            //         $('.min-visit-input').prop('disabled', true);
            //         $('.edit-icon').removeClass('d-none');
            //         $('.save-icon').addClass('d-none');
            //     }
            // });
        });
    </script>


    <script>
        var posTable;
        var checkboxes;
        $(document).ready(function() {

            const assignSelected = document.querySelector(
                '[data-kt-pos-table-modal-select="assign_selected"]'
            );

            const assignAllBtn = document.getElementById('assign-multiple-pos-by-filters');

            const toolbarSelected = document.querySelector(
                '[data-kt-pos-table-modal-toolbar="selected"]'
            );
            const selectedCount = document.querySelector(
                '[data-kt-pos-table-modal-select="selected_count"]'
            );

            function convertQueryStringToObject(queryString) {
                var query = {};
                var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
                for (var i = 0; i < pairs.length; i++) {
                    var pair = pairs[i].split('=');
                    query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
                }
                return query;
            }

            $(document).on('click', '#kt_modal_assign_pos #assign-multiple-pos-by-filters', function(e) {
                var url = `{{ route('marketing.marketing.store', $marketing->id) }}`;
                var queryStringData = $(this).closest('#filterForm').serialize();
                var data = convertQueryStringToObject(queryStringData);

                const token = document.head.querySelector(
                    'meta[name="csrf-token"]'
                ).content;

                Swal.fire({
                    text: 'Bạn có chắc chắn muốn phân giao điểm bán hàng này không?',
                    icon: "warning",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "Đồng ý ",
                    cancelButtonText: "{{ __('Cancel') }}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-danger",
                        cancelButton: "btn fw-bold btn-active-light-primary",
                    },
                }).then(function(result) {
                    if (result.value) {

                        if (result.value) {

                            axios
                                .post(url, data)
                                .then((response) => {
                                    const data = response.data;
                                    if (data.success) {
                                        KTApp.notify({
                                            title: data.message ||
                                                "Đã phân giao thành công!",
                                        })
                                        window.location.reload();

                                    } else {
                                        KTApp.notify({
                                            icon: 'error',
                                            title: data.message ||
                                                 "Lỗi trong quá trình gửi yêu cầu."
                                        })
                                    }
                                })
                                .catch((error) => {
                                    console.error(
                                        "Lỗi trong quá trình gửi yêu cầu:",
                                        error
                                    );
                                });

                            const headerCheckbox =
                                table.querySelectorAll('[type="checkbox"]')[0];
                            headerCheckbox.checked = false;
                        }

                    }
                });
            });


            function loadModalContent() {
                let staffId = {{ $marketing->staff_id ?? 'null' }};
                let url = staffId ? `{{ route('marketing.getPosData', ['staffId' => $marketing->staff_id]) }}` :
                    `{{ route('marketing.getPosData') }}`;

                posTable = $('#kt_model_table_datatable_pos').DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: url,
                        data: function(d) {
                            d.branch = $('#modal-create-marketing-table-filter select[name="branch"]')
                                .val();
                            d.agency = $('#modal-create-marketing-table-filter select[name="agency"]')
                                .val();
                            // d.staff_id = $('#modal-create-marketing-table-filter select[name="staff"]')
                            //     .val();
                            d.excludePosIds = '@json($marketing->pos->pluck('posCode'))';
                        },
                    },
                    order: [
                        ['2', 'desc']
                    ],
                    columns: [{
                            data: null,
                            orderable: false,
                            searchable: false,
                            render: function(data, type, row) {
                                return `<div class="form-check form-check-sm form-check-custom form-check-solid">
                                    <input name="selected_items[]" class="form-check-input form-check-input-pos" type="checkbox"
                                           value="${row.posCode}"/>
                                </div>`;
                            }
                        },
                        {
                            data: 'code',
                            name: 'posCode',
                            searchable: true,
                        },
                        {
                            data: 'visit',
                            name: 'visit',
                            searchable: false,
                            orderable: true
                        }, // Last Visit
                        {
                            data: 'revenue.amount',
                            name: 'revenue',
                            searchable: false,
                            orderable: true
                        }, // Revenue
                        {
                            data: 'province',
                            name: 'province',
                            searchable: true,
                            orderable: true,
                            render: function(data, type, row) {
                                return data.name;
                            }
                        },
                        {
                            data: 'agency',
                            name: 'agency',
                            searchable: true,
                            orderable: true,
                            render: function(data, type, row) {
                                return data.name;
                            },
                            className: 'fs-7',
                        },
                        {
                            data: 'address',
                            name: 'address',
                            searchable: true,
                            orderable: false,
                            className: 'fs-7',
                        },

                    ],
                    initComplete: function() {
                        var searchDiv = $(".dataTables_filter");
                        searchDiv.find('input').on('keyup', function() {
                            posTable.search(this.value).draw();
                        });
                        var api = this.api();

                        $(document).on('change',
                            '#modal-create-marketing-table-filter input, #modal-create-marketing-table-filter select',
                            function() {
                                posTable.draw();
                            });
                    },
                    drawCallback: function() {
                        toggleToolbars();
                        checkboxes = document.querySelectorAll('.form-check-input-pos');

                        checkboxes.forEach((c) => {
                            c.addEventListener("click", function() {
                                setTimeout(function() {
                                    toggleToolbars();
                                }, 50);
                            });
                        });
                    },
                });

            }


            assignSelected.addEventListener("click", function() {

                Swal.fire({
                    text: 'Bạn có chắc chắn muốn phân giao điểm bán hàng đã chọn cho chương trình marketing này không?',
                    icon: "warning",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "Đồng ý ",
                    cancelButtonText: "{{ __('Cancel') }}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-danger",
                        cancelButton: "btn fw-bold btn-active-light-primary",
                    },

                }).then(function(result) {
                    if (result.value) {
                        const checkedValues = [];
                        checkboxes.forEach((c) => {
                            if (c.checked && c.value) {
                                checkedValues.push(c.value);
                            }
                        });

                        axios
                            .post('{{ route('marketing.assignPos', $marketing->id) }}', {
                                pos_ids: checkedValues,
                                _token: KTApp.csrfToken(),
                            })
                            .then((response) => {
                                const data = response.data;
                                if (data.success) {
                                    KTApp.notify({
                                        title: data.message ||
                                            "Đã phân giao thành công!",
                                    })
                                    window.location.reload();

                                } else {
                                    KTApp.notify({
                                        icon: 'error',
                                        title: data.message ||
                                            "Lỗi trong quá trình gửi yêu cầu."
                                    })
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    "Lỗi trong quá trình gửi yêu cầu:",
                                    error
                                );
                            });
                    }
                });

            });


            const toggleToolbars = () => {
                const allCheckboxes = document.querySelectorAll(
                    '#kt_model_table_datatable_pos tbody .form-check-input-pos');
                // Detect checkboxes state & count
                let checkedState = false;
                let count = 0;

                // Count checked boxes
                allCheckboxes.forEach((c) => {
                    if (c.checked) {
                        checkedState = true;
                        count++;
                    }
                });

                // Toggle toolbars
                if (checkedState) {
                    selectedCount.innerHTML = count;
                    assignAllBtn?.classList?.add("d-none");
                    toolbarSelected.classList.remove("d-none");
                } else {
                    assignAllBtn?.classList?.remove("d-none");
                    toolbarSelected.classList.add("d-none");
                }
            };

            loadModalContent();

        });
    </script>
@endpush
