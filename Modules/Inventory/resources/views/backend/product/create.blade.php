@extends('layouts.app')

@section('page-title', __('Thêm mới loại trang thiết bị'))
@section('page-heading', __('Thêm mới loại trang thiết bị'))

@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Thi<PERSON><PERSON> lập
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Trang thiết bị
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        <a href="{{ route('products.index') }}"><PERSON><PERSON><PERSON> trang thiết bị</a>
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">@yield('page-title')</li>
@stop

@section('style')
    <link href="{{ asset('assets/plugins/custom/datatables/datatables.bundle.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
    <div id="kt_app_content" class="app-content">

        <section class="forms">
            {{-- <div class="card-header d-flex align-items-center">
                <h4>@yield('page-title')</h4>
            </div> --}}
            <div class="">
                <form id="product-form">
                    <div class="form d-flex flex-column flex-lg-row fv-plugins-bootstrap5 fv-plugins-framework">
                        <div class="d-flex flex-column gap-7 gap-lg-10 w-100 w-lg-300px mb-7 me-lg-10"
                            style="min-width: 300px;">
                            <!--begin::Thumbnail settings-->

                            @include('partials.upload-thumbnail', [
                                'inputName' => 'image',
                            ])

                            <div class="d-none card card-flush py-4">
                                <!--begin::Card header-->
                                <div class="card-header">
                                    <!--begin::Card title-->
                                    <div class="card-title">
                                        <h2 class="required"> <strong> {{ trans('Device type') }}</strong>
                                        </h2>
                                    </div>
                                    <!--end::Card title-->
                                </div>
                                <!--end::Card header-->
                                <!--begin::Card body-->
                                <div class="card-body text-center pt-0">
                                    <div class="input-group">
                                        <select disabled name="type" data-control="select2" data-hide-search="true"
                                            required class="form-select mb-2 selectpicker" id="type">
                                            <option value="standard">{{ __('Standard') }}</option>
                                            <option value="combo">Combo</option>
                                            <option value="marketing">Marketing</option>
                                            {{-- <option value="digital">Digital</option>
                                        <option value="service">Service</option> --}}
                                        </select>
                                    </div>
                                </div>
                                <!--end::Card body-->
                            </div>

                            <div class=" card card-flush py-4">
                                <!--begin::Card header-->
                                <div class="card-header">
                                    <!--begin::Card title-->
                                    <div class="card-title">
                                        <h2 class=""><strong>Yêu cầu kiểm kê</strong></h2>
                                    </div>
                                    <!--end::Card title-->
                                </div>
                                <!--end::Card header-->
                                <!--begin::Card body-->
                                <div class="card-body text-center pt-0">
                                    <div class="input-group">
                                        <select name="inventory_requirement" data-control="select2" data-hide-search="true"
                                            class="form-select mb-2 selectpicker" id="type">
                                            @foreach (Modules\Inventory\app\Models\Product::INVENTORY_LABELS as $key => $label)
                                                <option value="{{ $key }}">{{ $label }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <!--end::Card body-->
                            </div>

                            <div class="card card-flush py-4">
                                <!--begin::Card header-->
                                <div class="card-header">
                                    <!--begin::Card title-->
                                    <div class="card-title">
                                        <h2>{{ __('Config') }}</h2>
                                    </div>
                                    <!--end::Card title-->
                                </div>
                                <div class="card-body pt-0">
                                    <!--begin::Input group-->
                                    {{-- <div class="fv-row">
                                        <!--begin::Label-->
                                        <label class="form-label required ">{{ __('Device type') }}</label>
                                        <!--end::Label-->
                                        <!--begin::Select2-->
                                        <select name="category_id" class="form-select mb-2" data-control="select2"
                                            data-placeholder="{{ __('Select an option') }}">
                                            @foreach ($lims_category_list as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div> --}}
                                    <!--end::Select2-->
                                    <!--end::Input group-->
                                    <!--begin::Button-->
                                    {{-- <a href="{{ route('category.index') }}" class="btn btn-light-primary btn-sm mb-10">
                                        <i class="ki-duotone ki-plus fs-2"></i>{{ __('Create new category') }}</a> --}}
                                    <!--end::Button-->
                                    <!--begin::Input group-->
                                    <!--begin::Label-->
                                    {{-- <div class="fv-row">
                                        <label class="form-label d-block required">{{ __('Barcode symbol') }}</label>
                                        <!--end::Label-->
                                        <!--begin::Input-->
                                        <select name="barcode_symbology" class="form-select mb-10 fv-row"
                                            data-control="select2" data-hide-search="true"
                                            data-placeholder="{{ __('Select an option') }}">
                                            <option value="C128">Code 128</option>
                                            <option value="C39">Code 39</option>
                                            <option value="UPCA">UPC-A</option>
                                            <option value="UPCE">UPC-E</option>
                                            <option value="EAN8">EAN-8</option>
                                            <option value="EAN13">EAN-13</option>
                                        </select>
                                        <!--end::Input-->
                                    </div> --}}

                                    <div id="unit" class="fv-row">
                                        <label class="form-label d-block required">{{ __('Unit') }}</label>
                                        <!--end::Label-->
                                        <!--begin::Input-->
                                        <select name="unit_id" class="form-select mb-2 fv-row" data-control="select2"
                                            data-hide-search="true" data-placeholder="{{ __('Select an option') }}">
                                            @foreach ($lims_unit_list as $unit)
                                                @if ($unit->base_unit == null)
                                                    <option value="{{ $unit->id }}">{{ $unit->unit_name }}
                                                    </option>
                                                @endif
                                            @endforeach
                                        </select>
                                        <a href="{{ route('unit.index') }}" class="btn btn-light-primary btn-sm mt-2">
                                            <i class="ki-duotone ki-plus fs-2"></i>{{ __('Create new unit') }}</a>
                                        <!--end::Input group-->
                                    </div>
                                    <!--end::Input-->

                                </div>
                            </div>

                        </div>

                        <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                            <div class="tab-pane fade show active" id="kt_ecommerce_add_product_general" role="tab-panel">
                                <div class="d-flex flex-column gap-7 gap-lg-10">
                                    <!--begin::General options-->
                                    <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h2>{{ __('Detail information') }}</h2>
                                            </div>
                                        </div>
                                        <div class="card-body pt-0">
                                            <!--begin::Input group-->
                                            <div class="mb-10 fv-row">
                                                <!--begin::Label-->
                                                <label class="required form-label required">{{ __('Name') }}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input type="text" name="name" class="form-control mb-2"
                                                    value="" />
                                                <!--end::Input-->
                                            </div>

                                            <div class="d-flex flex-wrap gap-5 mb-10">
                                                <div class="fv-row w-100 flex-md-root">
                                                    <label
                                                        class="required form-label required">{{ __('Product code') }}</label>
                                                    <!--end::Label-->
                                                    <!--begin::Input-->
                                                    <input type="text" name="code" class="form-control mb-2"
                                                        placeholder="" value="" />
                                                </div>
                                                {{-- <div class="fv-row w-100 flex-md-root">
                                                    <label class="required form-label required">Nhà cung cấp</label>
                                                    <!--end::Label-->
                                                    <!--begin::Input-->
                                                    <select class="form-select mb-2" multiple data-close-on-select="false" name="supplier_id[]" id="supplier_id"
                                                        data-control="select2">
                                                        @foreach ($suppliers as $supplier)
                                                            <option value="{{ $supplier->id }}">{{ $supplier->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div> --}}

                                                <div class="fv-row w-100 flex-md-root">
                                                    <label class="required form-label">Loại</label>
                                                    <select name="supplier_type" data-control="select2" data-hide-search="true"
                                                        required class="form-select mb-2 selectpicker" id="supplier_type">
                                                        @foreach (Modules\Inventory\app\Models\Supplier::TYPE_LABELS as $type => $label)
                                                            <option value="{{ $type }}">{{ $label }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>

                                            </div>

                                            <div class="d-flex flex-wrap gap-5 mb-10">
                                                <div class="fv-row w-100 flex-md-root">
                                                    <label class=" form-label">Số tháng bảo hành</label>
                                                    <input type="number" name="month_warranty" class="form-control mb-2"
                                                        min="1" max="12" value="" />
                                                    <div
                                                        class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback">
                                                    </div>
                                                </div>
                                                <div class="fv-row w-100 flex-md-root">
                                                    <label class="form-label">Số tháng phân bổ</label>
                                                    <input type="number" name="month_allocation"
                                                        class="form-control mb-2" min="1" max="12"
                                                        value="" />
                                                </div>
                                            </div>

                                            <div class="mb-10 fv-row">
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <div
                                                            class="form-check form-check-solid form-check-custom form-switch w-25 ">
                                                            <input type="hidden" name="is_imei" value="0">
                                                            <input class="form-check-input w-45px h-30px" type="checkbox"
                                                                value="1" name="is_imei">
                                                            <span class="ms-3">Có gắn tem</span>
                                                        </div>
                                                    </div>

                                                    <span class="validation-msg"></span>
                                                </div>
                                            </div>

                                            <div>
                                                <label class="form-label">{{ __('Describe') }}</label>

                                                <div id="product_details" name="product_details"
                                                    class="min-h-200px mb-2">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::General options-->
                                    <!--begin::Media-->
                                    {{-- <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h2>{{ __('Device image') }}</h2>
                                            </div>
                                        </div>
                                        <!--end::Card header-->
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0">
                                            <!--begin::Input group-->
                                            @include('partials.upload-multi', [
                                                'nameInput' => 'image',
                                            ])
                                        </div>
                                        <!--end::Card header-->
                                    </div> --}}

                                </div>
                            </div>
                            <div class="d-flex justify-content-end">
                                @include('components.btn-action', [
                                    'btnSubmitId' => 'submit-btn',
                                    'btnCancelUrl' => route('products.index'),
                                ])
                            </div>
                        </div>

                    </div>

                </form>
            </div>
        </section>
    </div>
@endsection

@section('script-vendor')
    <script src="{{ asset('assets/plugins/custom/formrepeater/formrepeater.bundle.js') }}"></script>
    <!--end::Vendors Javascript-->
    {{-- <script src="{{ asset('assets/js/custom/apps/product/save-product.js') }}"></script> --}}

@stop

@push('script-component')
    <script type="text/javascript">
        const form = document.getElementById("product-form");
        $("ul#product").siblings('a').attr('aria-expanded', 'true');
        $("ul#product").addClass("show");
        $("ul#product #product-create-menu").addClass("active");



        $("#digital").hide();
        $("#combo").hide();
        $("#variant-section").hide();
        $("#initial-stock-section").hide();
        $("#diffPrice-section").hide();
        $("#promotion_price").hide();
        $("#start_date").hide();
        $("#last_date").hide();
        var variantPlaceholder = <?php echo json_encode(trans('file.Enter variant value seperated by comma')); ?>;
        var variantIds = [];
        var combinations = [];
        var oldCombinations = [];
        var oldAdditionalCost = [];
        var oldAdditionalPrice = [];
        var step;
        var numberOfWarehouse = <?php echo json_encode(count($lims_warehouse_list)); ?>;

        // $('[data-toggle="tooltip"]').tooltip();

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $('#genbutton').on("click", function() {
            $.get('gencode', function(data) {
                $("input[name='code']").val(data);
            });
        });

        $('.add-more-variant').on("click", function() {
            var htmlText =
                '<div class="col-md-4 form-group mt-2"><label class="form-label required">Option </label><input type="text" name="variant_option[]" class="form-control mb-2 variant-field" placeholder="Size, Color etc..."></div><div class="col-md-6 form-group mt-2"><label class="form-label required">Value </label><input type="text" name="variant_value[]" class="type-variant form-control mb-2 variant-field"></div>';
            $("#variant-input-section").append(htmlText);
            $('.type-variant').tagsInput();
        });

        //start variant related js
        $(function() {
            $('.type-variant').tagsInput();
        });

        (function($) {
            var delimiter = [];
            var inputSettings = [];
            var callbacks = [];

            $.fn.addTag = function(value, options) {
                options = jQuery.extend({
                    focus: false,
                    callback: true
                }, options);
                this.each(function() {
                    var id = $(this).attr('id');
                    var tagslist = $(this).val().split(_getDelimiter(delimiter[id]));
                    if (tagslist[0] === '') tagslist = [];

                    value = jQuery.trim(value);

                    if ((inputSettings[id].unique && $(this).tagExist(value)) || !_validateTag(
                            value,
                            inputSettings[id], tagslist, delimiter[id])) {
                        $('#' + id + '_tag').addClass('error');
                        return false;
                    }

                    $('<span>', {
                        class: 'tag'
                    }).append(
                        $('<span>', {
                            class: 'tag-text'
                        }).text(value),
                        $('<button>', {
                            class: 'tag-remove'
                        }).click(function() {
                            return $('#' + id).removeTag(encodeURI(value));
                        })
                    ).insertBefore('#' + id + '_addTag');
                    tagslist.push(value);

                    $('#' + id + '_tag').val('');
                    if (options.focus) {
                        $('#' + id + '_tag').focus();
                    } else {
                        $('#' + id + '_tag').blur();
                    }

                    $.fn.tagsInput.updateTagsField(this, tagslist);

                    if (options.callback && callbacks[id] && callbacks[id]['onAddTag']) {
                        var f = callbacks[id]['onAddTag'];
                        f.call(this, this, value);
                    }

                    if (callbacks[id] && callbacks[id]['onChange']) {
                        var i = tagslist.length;
                        var f = callbacks[id]['onChange'];
                        f.call(this, this, value);
                    }

                    $(".type-variant").each(function(index) {
                        variantIds.splice(index, 1, $(this).attr('id'));
                    });

                    //start custom code
                    first_variant_values = $('#' + variantIds[0]).val().split(_getDelimiter(
                        delimiter[
                            variantIds[0]]));
                    combinations = first_variant_values;
                    step = 1;
                    while (step < variantIds.length) {
                        var newCombinations = [];
                        for (var i = 0; i < combinations.length; i++) {
                            new_variant_values = $('#' + variantIds[step]).val().split(
                                _getDelimiter(
                                    delimiter[variantIds[step]]));
                            for (var j = 0; j < new_variant_values.length; j++) {
                                newCombinations.push(combinations[i] + '/' + new_variant_values[
                                    j]);
                            }
                        }
                        combinations = newCombinations;
                        step++;
                    }
                    var rownumber = $('table.variant-list tbody tr:last').index();
                    if (rownumber > -1) {
                        oldCombinations = [];
                        oldAdditionalCost = [];
                        oldAdditionalPrice = [];
                        $(".variant-name").each(function(i) {
                            oldCombinations.push($(this).text());
                            oldAdditionalCost.push($(
                                'table.variant-list tbody tr:nth-child(' + (i +
                                    1) + ')').find('.additional-cost').val());
                            oldAdditionalPrice.push($(
                                    'table.variant-list tbody tr:nth-child(' + (
                                        i + 1) + ')').find('.additional-price')
                                .val());
                        });
                    }
                    $("table.variant-list tbody").remove();
                    var newBody = $("<tbody>");
                    for (i = 0; i < combinations.length; i++) {
                        var variant_name = combinations[i];
                        var item_code = variant_name + '-' + $("#code").val();
                        var newRow = $("<tr>");
                        var cols = '';
                        cols += '<td class="variant-name">' + variant_name +
                            '<input type="hidden" name="variant_name[]" value="' +
                            variant_name +
                            '" /></td>';
                        cols +=
                            '<td><input type="text" class="form-control mb-2 item-code" name="item_code[]" value="' +
                            item_code + '" /></td>';
                        //checking if this variant already exist in the variant table
                        oldIndex = oldCombinations.indexOf(combinations[i]);
                        if (oldIndex >= 0) {
                            cols +=
                                '<td><input type="number" class="form-control mb-2 additional-cost" name="additional_cost[]" value="' +
                                oldAdditionalCost[oldIndex] + '" step="any" /></td>';
                            cols +=
                                '<td><input type="number" class="form-control mb-2 additional-price" name="additional_price[]" value="' +
                                oldAdditionalPrice[oldIndex] + '" step="any" /></td>';
                        } else {
                            cols +=
                                '<td><input type="number" class="form-control mb-2 additional-cost" name="additional_cost[]" value="" step="any" /></td>';
                            cols +=
                                '<td><input type="number" class="form-control mb-2 additional-price" name="additional_price[]" value="" step="any" /></td>';
                        }
                        newRow.append(cols);
                        newBody.append(newRow);
                    }
                    $("table.variant-list").append(newBody);
                    //end custom code
                });
                return false;
            };

            $.fn.removeTag = function(value) {
                value = decodeURI(value);

                this.each(function() {
                    var id = $(this).attr('id');

                    var old = $(this).val().split(_getDelimiter(delimiter[id]));

                    $('#' + id + '_tagsinput .tag').remove();

                    var str = '';
                    for (i = 0; i < old.length; ++i) {
                        if (old[i] != value) {
                            str = str + _getDelimiter(delimiter[id]) + old[i];
                        }
                    }

                    $.fn.tagsInput.importTags(this, str);

                    if (callbacks[id] && callbacks[id]['onRemoveTag']) {
                        var f = callbacks[id]['onRemoveTag'];
                        f.call(this, this, value);
                    }
                });

                return false;
            };

            $.fn.tagExist = function(val) {
                var id = $(this).attr('id');
                var tagslist = $(this).val().split(_getDelimiter(delimiter[id]));
                return (jQuery.inArray(val, tagslist) >= 0);
            };

            $.fn.importTags = function(str) {
                var id = $(this).attr('id');
                $('#' + id + '_tagsinput .tag').remove();
                $.fn.tagsInput.importTags(this, str);
            };

            $.fn.tagsInput = function(options) {
                var settings = jQuery.extend({
                    interactive: true,
                    placeholder: variantPlaceholder,
                    minChars: 0,
                    maxChars: null,
                    limit: null,
                    validationPattern: null,
                    width: 'auto',
                    height: 'auto',
                    autocomplete: null,
                    hide: true,
                    delimiter: ',',
                    unique: true,
                    removeWithBackspace: true
                }, options);

                var uniqueIdCounter = 0;

                this.each(function() {
                    if (typeof $(this).data('tagsinput-init') !== 'undefined') return;

                    $(this).data('tagsinput-init', true);

                    if (settings.hide) $(this).hide();

                    var id = $(this).attr('id');
                    if (!id || _getDelimiter(delimiter[$(this).attr('id')])) {
                        id = $(this).attr('id', 'tags' + new Date().getTime() + (++
                            uniqueIdCounter)).attr(
                            'id');
                    }

                    var data = jQuery.extend({
                        pid: id,
                        real_input: '#' + id,
                        holder: '#' + id + '_tagsinput',
                        input_wrapper: '#' + id + '_addTag',
                        fake_input: '#' + id + '_tag'
                    }, settings);

                    delimiter[id] = data.delimiter;
                    inputSettings[id] = {
                        minChars: settings.minChars,
                        maxChars: settings.maxChars,
                        limit: settings.limit,
                        validationPattern: settings.validationPattern,
                        unique: settings.unique
                    };

                    if (settings.onAddTag || settings.onRemoveTag || settings.onChange) {
                        callbacks[id] = [];
                        callbacks[id]['onAddTag'] = settings.onAddTag;
                        callbacks[id]['onRemoveTag'] = settings.onRemoveTag;
                        callbacks[id]['onChange'] = settings.onChange;
                    }

                    var markup = $('<div>', {
                        id: id + '_tagsinput',
                        class: 'tagsinput'
                    }).append(
                        $('<div>', {
                            id: id + '_addTag'
                        }).append(
                            settings.interactive ? $('<input>', {
                                id: id + '_tag',
                                class: 'form-control mb-2 tag-input',
                                value: '',
                                placeholder: settings.placeholder
                            }) : null
                        )
                    );

                    $(markup).insertAfter(this);

                    $(data.holder).css('width', settings.width);
                    $(data.holder).css('min-height', settings.height);
                    $(data.holder).css('height', settings.height);

                    if ($(data.real_input).val() !== '') {
                        $.fn.tagsInput.importTags($(data.real_input), $(data.real_input).val());
                    }

                    // Stop here if interactive option is not chosen
                    if (!settings.interactive) return;

                    $(data.fake_input).val('');
                    $(data.fake_input).data('pasted', false);

                    $(data.fake_input).on('focus', data, function(event) {
                        $(data.holder).addClass('focus');

                        if ($(this).val() === '') {
                            $(this).removeClass('error');
                        }
                    });

                    $(data.fake_input).on('blur', data, function(event) {
                        $(data.holder).removeClass('focus');
                    });

                    if (settings.autocomplete !== null && jQuery.ui.autocomplete !==
                        undefined) {
                        $(data.fake_input).autocomplete(settings.autocomplete);
                        $(data.fake_input).on('autocompleteselect', data, function(event, ui) {
                            $(event.data.real_input).addTag(ui.item.value, {
                                focus: true,
                                unique: settings.unique
                            });

                            return false;
                        });

                        $(data.fake_input).on('keypress', data, function(event) {
                            if (_checkDelimiter(event)) {
                                $(this).autocomplete("close");
                            }
                        });
                    } else {
                        $(data.fake_input).on('blur', data, function(event) {
                            $(event.data.real_input).addTag($(event.data.fake_input)
                                .val(), {
                                    focus: true,
                                    unique: settings.unique
                                });

                            return false;
                        });
                    }

                    // If a user types a delimiter create a new tag
                    $(data.fake_input).on('keypress', data, function(event) {
                        if (_checkDelimiter(event)) {
                            event.preventDefault();

                            $(event.data.real_input).addTag($(event.data.fake_input)
                                .val(), {
                                    focus: true,
                                    unique: settings.unique
                                });

                            return false;
                        }
                    });

                    $(data.fake_input).on('paste', function() {
                        $(this).data('pasted', true);
                    });

                    // If a user pastes the text check if it shouldn't be splitted into tags
                    $(data.fake_input).on('input', data, function(event) {
                        if (!$(this).data('pasted')) return;

                        $(this).data('pasted', false);

                        var value = $(event.data.fake_input).val();

                        value = value.replace(/\n/g, '');
                        value = value.replace(/\s/g, '');

                        var tags = _splitIntoTags(event.data.delimiter, value);

                        if (tags.length > 1) {
                            for (var i = 0; i < tags.length; ++i) {
                                $(event.data.real_input).addTag(tags[i], {
                                    focus: true,
                                    unique: settings.unique
                                });
                            }

                            return false;
                        }
                    });

                    // Deletes last tag on backspace
                    data.removeWithBackspace && $(data.fake_input).on('keydown', function(
                        event) {
                        if (event.keyCode == 8 && $(this).val() === '') {
                            event.preventDefault();
                            var lastTag = $(this).closest('.tagsinput').find(
                                    '.tag:last > span')
                                .text();
                            var id = $(this).attr('id').replace(/_tag$/, '');
                            $('#' + id).removeTag(encodeURI(lastTag));
                            $(this).trigger('focus');
                        }
                    });

                    // Removes the error class when user changes the value of the fake input
                    $(data.fake_input).keydown(function(event) {
                        // enter, alt, shift, esc, ctrl and arrows keys are ignored
                        if (jQuery.inArray(event.keyCode, [13, 37, 38, 39, 40, 27, 16,
                                17, 18,
                                225
                            ]) === -1) {
                            $(this).removeClass('error');
                        }
                    });
                });

                return this;
            };

            $.fn.tagsInput.updateTagsField = function(obj, tagslist) {
                var id = $(obj).attr('id');
                $(obj).val(tagslist.join(_getDelimiter(delimiter[id])));
            };

            $.fn.tagsInput.importTags = function(obj, val) {
                $(obj).val('');

                var id = $(obj).attr('id');
                var tags = _splitIntoTags(delimiter[id], val);

                for (i = 0; i < tags.length; ++i) {
                    $(obj).addTag(tags[i], {
                        focus: false,
                        callback: false
                    });
                }

                if (callbacks[id] && callbacks[id]['onChange']) {
                    var f = callbacks[id]['onChange'];
                    f.call(obj, obj, tags);
                }
            };

            var _getDelimiter = function(delimiter) {
                if (typeof delimiter === 'undefined') {
                    return delimiter;
                } else if (typeof delimiter === 'string') {
                    return delimiter;
                } else {
                    return delimiter[0];
                }
            };

            var _validateTag = function(value, inputSettings, tagslist, delimiter) {
                var result = true;

                if (value === '') result = false;
                if (value.length < inputSettings.minChars) result = false;
                if (inputSettings.maxChars !== null && value.length > inputSettings.maxChars) result =
                    false;
                if (inputSettings.limit !== null && tagslist.length >= inputSettings.limit) result =
                    false;
                if (inputSettings.validationPattern !== null && !inputSettings.validationPattern.test(
                        value))
                    result = false;

                if (typeof delimiter === 'string') {
                    if (value.indexOf(delimiter) > -1) result = false;
                } else {
                    $.each(delimiter, function(index, _delimiter) {
                        if (value.indexOf(_delimiter) > -1) result = false;
                        return false;
                    });
                }

                return result;
            };

            var _checkDelimiter = function(event) {
                var found = false;

                if (event.which === 13) {
                    return true;
                }

                if (typeof event.data.delimiter === 'string') {
                    if (event.which === event.data.delimiter.charCodeAt(0)) {
                        found = true;
                    }
                } else {
                    $.each(event.data.delimiter, function(index, delimiter) {
                        if (event.which === delimiter.charCodeAt(0)) {
                            found = true;
                        }
                    });
                }

                return found;
            };

            var _splitIntoTags = function(delimiter, value) {
                if (value === '') return [];

                if (typeof delimiter === 'string') {
                    return value.split(delimiter);
                } else {
                    var tmpDelimiter = '∞';
                    var text = value;

                    $.each(delimiter, function(index, _delimiter) {
                        text = text.split(_delimiter).join(tmpDelimiter);
                    });

                    return text.split(tmpDelimiter);
                }

                return [];
            };
        })(jQuery);


        var quill = new Quill('#product_details', {
            modules: {
                toolbar: [
                    [{
                        header: [1, 2, false],
                    }, ],
                    ["bold", "italic", "underline"],
                    ["image", "code-block"],
                ],
            },
            placeholder: "Nhập mô tả ở đây...",
            theme: "snow", // or 'bubble'
        });

        $('select[name="type"]').on('change', function() {
            if ($(this).val() == 'combo') {
                $("input[name='cost']").prop('required', false);
                $("select[name='unit_id']").prop('required', false);
                hide();
                $("#combo").show(300);
                $("input[name='price']").prop('disabled', true);
                $("#is-variant").prop("checked", false);
                $("#is-diffPrice").prop("checked", false);
                $("#variant-section, #variant-option, #diffPrice-option, #diffPrice-section").hide(300);
                validator.addField('unit_id', {
                    validators: {},
                });
            } else if ($(this).val() == 'digital') {
                $("input[name='cost']").prop('required', false);
                $("select[name='unit_id']").prop('required', false);
                $("input[name='file']").prop('required', true);
                hide();
                $("#digital").show(300);
                $("#combo").hide(300);
                $("input[name='price']").prop('disabled', false);
                $("#is-variant").prop("checked", false);
                $("#is-diffPrice").prop("checked", false);
                $("#variant-section, #variant-option, #diffPrice-option, #diffPrice-section, #batch-option")
                    .hide(
                        300);
            } else if ($(this).val() == 'service') {
                $("input[name='cost']").prop('required', false);
                $("select[name='unit_id']").prop('required', false);
                $("input[name='file']").prop('required', true);
                hide();
                $("#combo").hide(300);
                $("#digital").hide(300);
                $("input[name='price']").prop('disabled', false);
                $("#is-variant").prop("checked", false);
                $("#is-diffPrice").prop("checked", false);
                $("#variant-section, #variant-option, #diffPrice-option, #diffPrice-section, #batch-option, #imei-option")
                    .hide(300);
            } else if ($(this).val() == 'standard') {
                $("input[name='cost']").prop('required', true);
                $("select[name='unit_id']").prop('required', true);
                $("input[name='file']").prop('required', false);
                $("#cost").show(300);
                $("#unit").show(300);
                $("#alert-qty").show(300);
                $("#variant-option, #diffPrice-option, #batch-option, #imei-option").show(300);
                $("#digital").hide(300);
                $("#combo").hide(300);
                $("input[name='price']").prop('disabled', false);
                validator.addField('unit_id', {
                    validators: {
                        notEmpty: {
                            message: "Đơn vị là bắt buộc",
                        },
                    },
                });
            } else if ($(this).val() == 'marketing') {
                $("input[name='cost']").prop('required', true);
                $("select[name='unit_id']").prop('required', true);
                $("input[name='file']").prop('required', false);
                $("#cost").show(300);
                $("#unit").show(300);
                $("#alert-qty").show(300);
                $("#variant-option, #diffPrice-option, #batch-option, #imei-option").show(300);
                $("#digital").hide(300);
                $("#combo").hide(300);
                $("input[name='price']").prop('disabled', false);
                validator.addField('unit_id', {
                    validators: {
                        notEmpty: {
                            message: "Đơn vị là bắt buộc",
                        },
                    },
                });
            }
        });

        $('select[name="unit_id"]').on('change', function() {

            unitID = $(this).val();
            if (unitID) {
                populate_category(unitID);
            } else {
                $('select[name="sale_unit_id"]').empty();
                $('select[name="purchase_unit_id"]').empty();
            }
        });
        <?php $productArray = []; ?>
        var lims_product_code = [
            @foreach ($lims_product_list_without_variant as $product)
                <?php
                $productArray[] = htmlspecialchars($product->code) . ' (' . preg_replace('/[\n\r]/', '<br>', htmlspecialchars($product->name)) . ')';
                ?>
            @endforeach
            @foreach ($lims_product_list_with_variant as $product)
                <?php
                $productArray[] = htmlspecialchars($product->item_code) . ' (' . preg_replace('/[\n\r]/', '<br>', htmlspecialchars($product->name)) . ')';
                ?>
            @endforeach
            <?php
            echo '"' . implode('","', $productArray) . '"';
            ?>
        ];


        //Change quantity or unit price
        $("#myTable").on('input', '.qty , .unit_price', function() {
            calculate_price();
        });

        //Delete product
        $("table.order-list tbody").on("click", ".ibtnDel", function(event) {
            $(this).closest("tr").remove();
            calculate_price();
        });

        function hide() {
            $("#cost").hide(300);
            $("#unit").hide(300);
            $("#alert-qty").hide(300);
        }

        function calculate_price() {
            var price = 0;
            $(".qty").each(function() {
                rowindex = $(this).closest('tr').index();
                quantity = $(this).val();
                unit_price = $('table.order-list tbody tr:nth-child(' + (rowindex + 1) +
                    ') .unit_price').val();
                price += quantity * unit_price;
            });
            $('input[name="price"]').val(price);
        }

        function populate_category(unitID) {
            $.ajax({
                url: 'saleunit/' + unitID,
                type: "GET",
                dataType: "json",
                success: function(data) {
                    $('select[name="sale_unit_id"]').empty();
                    $('select[name="purchase_unit_id"]').empty();
                    $.each(data, function(key, value) {
                        $('select[name="sale_unit_id"]').append('<option value="' + key +
                            '">' + value +
                            '</option>');
                        $('select[name="purchase_unit_id"]').append('<option value="' +
                            key + '">' +
                            value + '</option>');
                    });
                    // $('.selectpicker').selectpicker('refresh');
                },
            });
        }

        $("input[name='is_initial_stock']").on("change", function() {
            if ($(this).is(':checked')) {
                if (numberOfWarehouse > 0)
                    $("#initial-stock-section").show(300);
                else {
                    alert('Please create warehouse first before adding stock!');
                    $(this).prop("checked", false);
                }
            } else {
                $("#initial-stock-section").hide(300);
            }
        });

        $("input[name='is_batch']").on("change", function() {
            if ($(this).is(':checked')) {
                $("#variant-option").hide(300);
            } else
                $("#variant-option").show(300);
        });

        $("input[name='is_variant']").on("change", function() {
            if ($(this).is(':checked')) {
                $("#variant-section").show(300);
                $("#batch-option").hide(300);
                $(".variant-field").prop("required", true);
            } else {
                $("#variant-section").hide(300);
                $("#batch-option").show(300);
                $(".variant-field").prop("required", false);
            }
        });

        $("input[name='is_diffPrice']").on("change", function() {
            if ($(this).is(':checked')) {
                $("#diffPrice-section").show(300);
            } else
                $("#diffPrice-section").hide(300);
        });

        $("#promotion").on("change", function() {
            if ($(this).is(':checked')) {
                // $("#starting_date").val($.datepicker.formatDate('dd-mm-yy', new Date()));
                $("#promotion_price").show(300);
                $("#start_date").show(300);
                $("#last_date").show(300);
            } else {
                $("#promotion_price").hide(300);
                $("#start_date").hide(300);
                $("#last_date").hide(300);
            }
        });

        var starting_date = $('#starting_date');
        // starting_date.datepicker({
        //     format: "dd-mm-yyyy",
        //     startDate: "<?php echo date('d-m-Y'); ?>",
        //     autoclose: true,
        //     todayHighlight: true
        // });

        var ending_date = $('#ending_date');
        // ending_date.datepicker({
        //     format: "dd-mm-yyyy",
        //     startDate: "<?php echo date('d-m-Y'); ?>",
        //     autoclose: true,
        //     todayHighlight: true
        // });

        $(window).keydown(function(e) {
            if (e.which == 13) {
                var $targ = $(e.target);

                if (!$targ.is("textarea") && !$targ.is(":button,:submit")) {
                    var focusNext = false;
                    $(this).find(":input:visible:not([disabled],[readonly]), a").each(function() {
                        if (this === e.target) {
                            focusNext = true;
                        } else if (focusNext) {
                            $(this).focus();
                            return false;
                        }
                    });

                    return false;
                }
            }
        });
        //dropzone portion
        Dropzone.autoDiscover = false;

        // jQuery.validator.setDefaults({
        //     errorPlacement: function(error, element) {
        //         if (error.html() == 'Select Category...')
        //             error.html('This field is required.');
        //         $(element).closest('div.form-group').find('.validation-msg').html(error.html());
        //     },
        //     highlight: function(element) {
        //         $(element).closest('div.form-group').removeClass('has-success').addClass('has-error');
        //     },
        //     unhighlight: function(element, errorClass, validClass) {
        //         $(element).closest('div.form-group').removeClass('has-error').addClass('has-success');
        //         $(element).closest('div.form-group').find('.validation-msg').html('');
        //     }
        // });

        function validate() {
            var product_code = $("input[name='code']").val();
            var barcode_symbology = $('select[name="barcode_symbology"]').val();
            var exp = /^\d+$/;

            if (!(product_code.match(exp)) && (barcode_symbology == 'UPCA' || barcode_symbology == 'UPCE' ||
                    barcode_symbology == 'EAN8' || barcode_symbology == 'EAN13')) {
                alert('Product code must be numeric.');
                return false;
            } else if (product_code.match(exp)) {
                if (barcode_symbology == 'UPCA' && product_code.length > 11) {
                    alert('Product code length must be less than 12');
                    return false;
                } else if (barcode_symbology == 'EAN8' && product_code.length > 7) {
                    alert('Product code length must be less than 8');
                    return false;
                } else if (barcode_symbology == 'EAN13' && product_code.length > 12) {
                    alert('Product code length must be less than 13');
                    return false;
                }
            }

            if ($("#type").val() == 'combo') {
                var rownumber = $('table.order-list tbody tr:last').index();
                if (rownumber < 0) {
                    alert("Please insert product to table!")
                    return false;
                }
            }
            if ($("#is-variant").is(":checked")) {
                rowindex = $("table#variant-table tbody tr:last").index();
                if (rowindex < 0) {
                    alert('This product has variant. Please insert variant to table');
                    return false;
                }
            }
            $("input[name='price']").prop('disabled', false);
            return true;
        }

        // $(".dropzone").sortable({
        //     items: '.dz-preview',
        //     cursor: 'grab',
        //     opacity: 0.5,
        //     containment: '.dropzone',
        //     distance: 20,
        //     tolerance: 'pointer',
        //     stop: function() {
        //         var queue = myDropzone.getAcceptedFiles();
        //         newQueue = [];
        //         $('#imageUpload .dz-preview .dz-filename [data-dz-name]').each(function(count, el) {
        //             var name = el.innerHTML;
        //             queue.forEach(function(file) {
        //                 if (file.name === name) {
        //                     newQueue.push(file);
        //                 }
        //             });
        //         });
        //         myDropzone.files = newQueue;
        //     }
        // });


        // Define variables
        let validator;

        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
        validator = FormValidation.formValidation(form, {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: "Tên là bắt buộc",
                        },
                    },
                },
                code: {
                    validators: {
                        notEmpty: {
                            message: "Mã là bắt buộc",
                        },
                    },
                },
                price: {
                    validators: {
                        notEmpty: {
                            message: "Giá trang thiết bị là bắt buộc",
                        },
                    },
                },
                cost: {
                    validators: {
                        notEmpty: {
                            message: "Giá trang thiết bị là bắt buộc",
                        },
                    },
                },
                category_id: {
                    validators: {
                        notEmpty: {
                            message: "Danh mục là bắt buộc",
                        },
                    },
                },
                "supplier_id[]": {
                    validators: {
                        notEmpty: {
                            message: "Nhà cung cấp là bắt buộc",
                        },
                    },
                },

            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap5({
                    rowSelector: ".fv-row",
                    eleInvalidClass: "",
                    eleValidClass: "",
                }),
            },
        });


        $('#submit-btn').on("click", function(e) {
            e.preventDefault();
            const submitBtn = document.getElementById("submit-btn")
            if (validator) {
                validator.validate().then(function(status) {
                    if (status == "Valid") {
                        const form = $("#product-form");
                        const unitId = document.querySelector(
                            "select[name='unit_id']"
                        );
                        e.preventDefault();
                        submitBtn.setAttribute("data-kt-indicator", "on");
                        submitBtn.disabled = true;

                        if (validate()) {
                            let quillContent = quill.root.innerHTML;
                            var formData = new FormData();

                            var data = $("#product-form").serializeArray();
                            $.each(data, function(key, el) {
                                formData.append(el.name, el.value);
                            });

                            // var file = $('#file')[0].files;
                            // if (file.length > 0) {
                            //     formData.append('file', file[0]);
                            // }

                            formData.append("product_details", quillContent);
                            formData.append("starting_date", "");
                            formData.append("last_date", "");
                            formData.append("purchase_unit_id", unitId.value);
                            formData.append("sale_unit_id", unitId.value);
                            formData.append("cost", 1);
                            formData.append("price", 1);
                            formData.append("qty", 1);

                            $.ajax({
                                type: 'POST',
                                url: '{{ route('products.store') }}',
                                data: formData,
                                contentType: false,
                                processData: false,
                                success: function(response) {

                                    submitBtn.disabled = false;
                                    submitBtn.setAttribute(
                                        "data-kt-indicator",
                                        "off"
                                    );
                                    location.href = '../products';
                                },
                                error: function(response) {
                                    submitBtn.disabled = false;
                                    submitBtn.setAttribute(
                                        "data-kt-indicator",
                                        "off"
                                    );
                                    Swal.fire({
                                        toast: true,
                                        icon: "error",
                                        title: response
                                            .responseJSON
                                            .message,
                                        position: "top-end",
                                        showConfirmButton: false,
                                        timer: 3000,
                                        iconColor: "red",
                                        showClass: {
                                            popup: "animate__animated animate__fadeInDown",
                                        },
                                        hideClass: {
                                            popup: "animate__animated animate__fadeOutUp",
                                        },
                                        didOpen: (toast) => {
                                            toast
                                                .addEventListener(
                                                    "mouseenter",
                                                    Swal
                                                    .stopTimer
                                                );
                                            toast
                                                .addEventListener(
                                                    "mouseleave",
                                                    Swal
                                                    .resumeTimer
                                                );
                                        },
                                    })
                                },
                            });
                        }
                    }
                });
            }
        });


        // myDropzone = new Dropzone('div#kt_ecommerce_add_product_media', {
        //     addRemoveLinks: true,
        //     autoProcessQueue: false,
        //     uploadMultiple: true,
        //     parallelUploads: 100,
        //     maxFilesize: 12,
        //     paramName: 'image',
        //     clickable: true,
        //     method: 'POST',
        //     url: '{{ route('products.store') }}',
        //     headers: {
        //         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        //     },
        //     renameFile: function(file) {
        //         var dt = new Date();
        //         var time = dt.getTime();
        //         return time + file.name;
        //     },
        //     acceptedFiles: ".jpeg,.jpg,.png,.gif",
        //     init: function() {
        //         var myDropzone = this;

        //         $('#submit-btn').on("click", function(e) {
        //             if (validator) {
        //                 validator.validate().then(function(status) {
        //                     if (status == "Valid") {
        //                         const form = $("#product-form");
        //                         const unitId = document.querySelector(
        //                             "select[name='unit_id']"
        //                         );
        //                         e.preventDefault();
        //                         if (validate()) {
        //                             // tinyMCE.triggerSave();
        //                             // if (myDropzone.getAcceptedFiles().length) {

        //                             //     myDropzone.processQueue();

        //                             // } else {
        //                             let quillContent = quill.root.innerHTML;
        //                             var formData = new FormData();

        //                             if (myDropzone.getAcceptedFiles().length) {
        //                                 var files = myDropzone.getQueuedFiles();
        //                                 for (var i = 0; i < files.length; i++) {
        //                                     formData.append(`image[${i}]`, files[i]);
        //                                 }
        //                             }

        //                             var data = $("#product-form").serializeArray();
        //                             $.each(data, function(key, el) {
        //                                 formData.append(el.name, el.value);
        //                             });

        //                             var file = $('#file')[0].files;
        //                             if (file.length > 0) {
        //                                 formData.append('file', file[0]);
        //                             }

        //                             formData.append("product_details", quillContent);
        //                             formData.append("starting_date", "");
        //                             formData.append("last_date", "");
        //                             formData.append("purchase_unit_id", unitId.value);
        //                             formData.append("sale_unit_id", unitId.value);
        //                             formData.append("cost", 1);
        //                             formData.append("price", 1);
        //                             formData.append("qty", 1);

        //                             $.ajax({
        //                                 type: 'POST',
        //                                 url: '{{ route('products.store') }}',
        //                                 data: formData,
        //                                 contentType: false,
        //                                 processData: false,
        //                                 success: function(response) {
        //                                     Swal.fire({
        //                                         toast: true,
        //                                         icon: "success",
        //                                         title: "Biểu mẫu đã được gửi thành công!",
        //                                         position: "top-end",
        //                                         showConfirmButton: false,
        //                                         timer: 3000,
        //                                         iconColor: "green",
        //                                         showClass: {
        //                                             popup: "animate__animated animate__fadeInDown",
        //                                         },
        //                                         hideClass: {
        //                                             popup: "animate__animated animate__fadeOutUp",
        //                                         },
        //                                         didOpen: (toast) => {
        //                                             toast
        //                                                 .addEventListener(
        //                                                     "mouseenter",
        //                                                     Swal
        //                                                     .stopTimer
        //                                                 );
        //                                             toast
        //                                                 .addEventListener(
        //                                                     "mouseleave",
        //                                                     Swal
        //                                                     .resumeTimer
        //                                                 );
        //                                         },
        //                                     })
        //                                     //console.log(response);
        //                                     location.href = '../products';
        //                                 },
        //                                 error: function(response) {
        //                                     Swal.fire({
        //                                         toast: true,
        //                                         icon: "error",
        //                                         title: response
        //                                             .responseJSON
        //                                             .message,
        //                                         position: "top-end",
        //                                         showConfirmButton: false,
        //                                         timer: 3000,
        //                                         iconColor: "red",
        //                                         showClass: {
        //                                             popup: "animate__animated animate__fadeInDown",
        //                                         },
        //                                         hideClass: {
        //                                             popup: "animate__animated animate__fadeOutUp",
        //                                         },
        //                                         didOpen: (toast) => {
        //                                             toast
        //                                                 .addEventListener(
        //                                                     "mouseenter",
        //                                                     Swal
        //                                                     .stopTimer
        //                                                 );
        //                                             toast
        //                                                 .addEventListener(
        //                                                     "mouseleave",
        //                                                     Swal
        //                                                     .resumeTimer
        //                                                 );
        //                                         },
        //                                     })
        //                                     // if (response.responseJSON.errors
        //                                     //     .name) {
        //                                     //     $("#name-error").text(response
        //                                     //         .responseJSON.errors
        //                                     //         .name);
        //                                     // } else if (response.responseJSON
        //                                     //     .errors
        //                                     //     .code) {
        //                                     //     $("#code-error").text(response
        //                                     //         .responseJSON.errors
        //                                     //         .code);
        //                                     // }
        //                                 },
        //                             });
        //                             // }
        //                         }
        //                     }
        //                 });
        //             }
        //         });

        //         this.on('sending', function(file, xhr, formData) {
        //             // Append all form inputs to the formData Dropzone will POST
        //             var data = $("#product-form").serializeArray();
        //             $.each(data, function(key, el) {
        //                 formData.append(el.name, el.value);
        //             });
        //             var file = $('#file')[0].files;
        //             if (file.length > 0)
        //                 formData.append('file', file[0]);
        //         });
        //     },
        //     error: function(file, response) {
        //         console.log(response);
        //         if (response.errors.name) {
        //             $("#name-error").text(response.errors.name);
        //             this.removeAllFiles(true);
        //         } else if (response.errors.code) {
        //             $("#code-error").text(response.errors.code);
        //             this.removeAllFiles(true);
        //         } else {
        //             try {
        //                 var res = JSON.parse(response);
        //                 if (typeof res.message !== 'undefined' && !$modal.hasClass('in')) {
        //                     $("#success-icon").attr("class", "fas fa-thumbs-down");
        //                     $("#success-text").html(res.message);
        //                     $modal.modal("show");
        //                 } else {
        //                     // if ($.type(response) === "string")
        //                     //     var message =
        //                     //         response; //dropzone sends it's own error messages in string
        //                     // else
        //                     //     var message = response.message;
        //                     // file.previewElement.classList.add("dz-error");
        //                     // _ref = file.previewElement.querySelectorAll(
        //                     //     "[data-dz-errormessage]");
        //                     // _results = [];
        //                     // for (_i = 0, _len = _ref.length; _i < _len; _i++) {
        //                     //     node = _ref[_i];
        //                     //     _results.push(node.textContent = message);
        //                     // }
        //                     // return _results;
        //                 }
        //             } catch (error) {
        //                 console.log(error);
        //             }
        //         }
        //     },
        //     successmultiple: function(file, response) {
        //         location.href = '../products';
        //         //console.log(file, response);
        //     },
        //     completemultiple: function(file, response) {
        //         console.log(file, response, "completemultiple");
        //     },
        //     reset: function() {
        //         console.log("resetFiles");
        //         this.removeAllFiles(true);
        //     }
        // });



        // JavaScript
        $(document).ready(function() {

            $('#lims_productCodeSearch').select2({
                ajax: {
                    url: '{{ route('api.products.selectProduct') }}',
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page || 1 // Trang hiện tại
                        };
                    },
                    processResults: function(data) {
                        var options = [];
                        if (data && data.results.length > 0) {
                            data.results.forEach(function(product) {
                                options.push({
                                    id: product.code,
                                    text: product.name
                                });
                            });
                        }
                        return {
                            results: options,
                            pagination: {
                                more: data.pagination.more // Kiểm tra xem còn trang nào không
                            }
                        };
                    },
                    cache: true
                },
                // minimumInputLength: 1 // Số ký tự tối thiểu trước khi gửi yêu cầu Ajax
            });


            var lims_productCodeSearch = $('#lims_productCodeSearch');

            lims_productCodeSearch.on('change', function() {
                var searchTerm = $(this).val(); // Lấy giá trị từ ô tìm kiếm

                $.ajax({
                    type: 'GET',
                    url: 'lims_product_search',
                    data: {
                        data: searchTerm
                    },
                    success: function(data) {
                        //console.log(data);
                        var flag = 1;
                        $(".product-id").each(function() {
                            if ($(this).val() == data[7]) {
                                alert(
                                    'Duplicate input is not allowed!')
                                flag = 0;
                            }
                        });
                        $("input[name='product_code_name']").val('');
                        if (flag) {
                            var newRow = $("<tr>");
                            var cols = '';
                            cols += '<td class="ps-3">' + data[0] + ' [' + data[1] +
                                ']</td>';
                            // cols +=
                            //     '<td><input type="number" class="form-control mb-2 qty" name="product_qty[]" value="1" step="any"/></td>';
                            // cols +=
                            //     '<td><input type="number" class="form-control mb-2 unit_price" name="unit_price[]" value="' +
                            //     data[2] + '" step="any"/></td>';
                            cols +=
                                '<td class="text-end pe-3"><button type="button" class="ibtnDel btn btn-sm btn-danger">X</button></td>';
                            cols +=
                                '<input type="hidden" class="product-id" name="product_id[]" value="' +
                                data[7] + '"/>';
                            cols +=
                                '<input type="hidden" class="" name="variant_id[]" value="' +
                                data[8] + '"/>';

                            newRow.append(cols);
                            $("table.order-list tbody").append(newRow);
                            calculate_price();
                        }

                    }
                }).done(function() {
                    lims_productCodeSearch.val('');
                });
            });

        });
    </script>
@endpush
