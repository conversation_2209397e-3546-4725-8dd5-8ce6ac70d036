@extends('layouts.app')

@section('page-title', '<PERSON>ập nhật thu hồi')
@section('page-heading', 'Cập nhật thu hồi')

@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Trang thiết bị
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">@yield('page-title')</li>
@stop

@section('header-actions')
    <div class="d-flex align-items-center gap-3 gap-lg-5">
        <!--begin::Primary button-->
        <div class="d-flex flex-stack flex-row-fluid justify-content-end pb-2">

        </div>
        <!--end::Primary button-->
    </div>
@stop


@section('content')
    <div id="kt_app_content" class="app-content">
        <div class="card">
            <section class="forms">
                <div class="card-header border-0 pt-6">
                    <h4>@yield('page-title')</h4>
                </div>
                <div class="card-body py-4">
                    {!! Form::open([
                        'route' => [checkType('recovery.update'), $recovery->id],
                        'method' => 'post',
                        'files' => true,
                        'id' => 'hand-over-form',
                        'enctype' => 'multipart/form-data',
                    ]) !!}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class="form-label required">Từ kho/ ĐBH</label>

                                        @include('inventory::components.select-warehouse', [
                                            'inputName' => 'from_warehouse_id',
                                            'selectId' => 'from_warehouse_id',
                                            'selectedWarehose' => $fromWarehouse,
                                        ])

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class=" form-label required">Về kho/ ĐBH</label>
                                        @include('inventory::components.select-warehouse', [
                                            'inputName' => 'to_warehouse_id',
                                            'selectId' => 'to_warehouse_id',
                                            'selectedWarehose' => $toWarehouse,
                                        ])
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class="form-label required">Ngày thu hồi</label>
                                        <input name="acceptance_date" class="form-control date"
                                            value="{{ $recovery->acceptance_date }}" id="kt_td_picker_date_only_input"
                                            placeholder="Chọn ngày" />
                                    </div>
                                </div>


                                <div class="col-md-4">

                                    <label class=" form-label">{{ trans('Select equipment') }}</label>
                                    <div class="search-box input-group">

                                        @include('inventory::components.selected-product-modal', [
                                            'warehouseSearchId' => 'from_warehouse_id',
                                            'itemAction' => Modules\Inventory\app\Constants\ItemAction::ACTION_RECOVER,
                                        ])

                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class=" form-label">Đính kèm tài liệu
                                            <span class="ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Only jpg, jpeg, png, gif, pdf, csv, docx, xlsx and txt file is supported">
                                                <i class="ki-duotone ki-information fs-7">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                            </span>
                                        </label>
                                        {{-- <input type="file" name="document" class="form-control mb-2"> --}}
                                        <div class=" mb-2">
                                            @include('partials.upload-file', [
                                               'inputName' => 'document',
                                                    'multiUpload' => true,
                                                    'files' => $recovery->document,
                                            ])
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="row mt-5">
                                <div class="col-md-12">
                                    <h5 class="required">Danh sách trang thiết bị</h5>

                                    @include('inventory::components.selected-product-table', [
                                        'products' => $recovery->productTransfers,
                                        'itemAction' => Modules\Inventory\app\Constants\ItemAction::ACTION_RECOVER,
                                        'transfer_id' => $recovery->id,
                                    ])


                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class="form-label">{{ trans('Note') }}</label>
                                        <textarea rows="5" class="form-control" name="note">{!! $recovery->note !!}</textarea>
                                    </div>
                                </div>
                            </div>


                            @include('inventory::components.btn-action', [
                                'btnCancelUrl' => route(checkType('recovery.create')),
                                'btnSubmitText' => 'Cập nhật',
                            ])

                        </div>
                    </div>
                    {!! Form::close() !!}
                </div>
            </section>
        </div>
    </div>
@stop

@section('style')
    {!! HTML::style(asset('assets/plugins/custom/datatables/datatables.bundle.css')) !!}
@endsection
@section('script')

    <script>
        $("#kt_td_picker_date_only_input").flatpickr({
            enableTime: false,
            dateFormat: "Y-m-d",
            altInput: true,
             altFormat: "d/m/Y",
        });
        $("#kt_td_picker_date_only_input_2").flatpickr({
            enableTime: false,
            dateFormat: "Y-m-d",
            altInput: true,
             altFormat: "d/m/Y",
        });
    </script>

    <script>
        "use strict";
        var ValidatorForm;
        var form = document.getElementById("hand-over-form");
        var submitButton = document.getElementById("submit-btn");

        var options = {
            fields: {
                from_warehouse_id: {
                    validators: {
                        notEmpty: {
                            message: "Trường này là bắt buộc",
                        },
                    },
                },
                to_warehouse_id: {
                    validators: {
                        notEmpty: {
                            message: "Trường này là bắt buộc",
                        },
                        different: {
                            message: "2 kho/ ĐBH phải khác nhau",
                            compare: function() {
                                return form.querySelector('[name="from_warehouse_id"]').value;
                            }
                        }
                    },
                },
                acceptance_date: {
                    validators: {
                        notEmpty: {
                            message: "Trường này là bắt buộc",
                        },
                    },
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap5({
                    rowSelector: ".fv-row",
                    eleInvalidClass: "",
                    eleValidClass: "",
                }),
            },
        }
        ValidatorForm = FormValidation.formValidation(form, options);

        // Class definition
        var KTTransfer = (function() {
            // Get elements

            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                },
            });

            // Function to handle form submission
            const handleSubmitForm = () => {

                submitButton.addEventListener("click", (e) => {
                    e.preventDefault();

                    const files = KTFile.updateFormValidationStatus();
                    console.log(files);
                    handleValidateTTB()

                    if (ValidatorForm) {

                        ValidatorForm.validate().then(function(status) {
                            if (status === "Valid") {

                                submitButton.setAttribute("data-kt-indicator", "on");
                                submitButton.disabled = true;
                                var formData = new FormData(form);

                                files.forEach(function(file) {

                                    if (file instanceof File) {
                                        formData.append('document[]', file, file.name);
                                    } else {
                                        formData.append('document_url[]', file.name);
                                    }
                                });

                                let jsonData = {};
                                $(form).serializeArray().forEach(field => {
                                    jsonData[field.name] = field.value;
                                });

                                formData.append('jsonData', JSON.stringify(jsonData));
                                var action = form.getAttribute("action");
                                axios
                                    .post(action, formData)
                                    .then(function({
                                        data
                                    }) {
                                        submitButton.disabled = false;
                                        submitButton.setAttribute(
                                            "data-kt-indicator",
                                            "off"
                                        );

                                        if (data.success) {
                                            KTApp.notify({
                                                title: data.message
                                            })
                                            window.location.onload();
                                            form.reset();

                                        } else {
                                            KTApp.notify({
                                                icon: 'error',
                                                title: data.message
                                            })
                                        }
                                    })
                                    .catch(function(error) {
                                        submitButton.disabled = false;
                                        submitButton.setAttribute(
                                            "data-kt-indicator",
                                            "off"
                                        );
                                        KTApp.handleError(error)
                                    });
                            }
                        });
                    }
                });
            };
            // Public methods
            return {
                init: function() {
                    handleSubmitForm();
                }
            };
        })();

        // On document ready
        KTUtil.onDOMContentLoaded(function() {
            KTTransfer.init();

        });
    </script>
@stop
