@extends('layouts.app')
@php
    $editable = $disposal->editable && auth()->user()->hasPermission(checkTypePms('dispose.update')) && $edit;
    $itemAction = Modules\Inventory\app\Constants\ItemAction::ACTION_DISPOSE;

    $prefix_title = $edit ? 'Cập nhật' : 'Chi tiết';
@endphp

@section('page-title', $prefix_title . ' xuất hủy')
@section('page-heading', $prefix_title . " phiếu {$disposal->reference_no}")


@section('breadcrumbs')
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">
        Trang thiết bị
    </li>
    <li class="breadcrumb-item">
        <i class="ki-duotone ki-right fs-3 text-gray-500 mx-n1"></i>
    </li>
    <li class="breadcrumb-item text-gray-600 fw-bold lh-1">@yield('page-title')</li>
@stop



@section('header-actions')
    @if (!$edit && $disposal->editable && auth()->user()->hasPermission(checkTypePms('transfer.update')))
        <a href="{{ route(checkType('disposal.edit'), $disposal->id) }}" class="btn btn-primary">Cập nhật</a>
    @endif
@stop

@section('content')
    <div id="kt_app_content" class="app-content">
        <div class="card">
            <section class="forms">

                <div class="card-body">
                    {!! Form::open([
                        'route' => [checkType('disposal.update'), $disposal->id],
                        'method' => 'post',
                        'files' => true,
                        'id' => 'hand-over-form',
                        'enctype' => 'multipart/form-data',
                    ]) !!}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row ">
                                        <label class="form-label required">Mã biên bản</label>
                                        <input value="{{ $disposal->reference_no }}"
                                               data-inputmask="'regex': '[^ ]*' , 'rightAlign': false "
                                               type="text" class="form-control" name="reference_no"
                                               placeholder="Nhập mã biên bản"/>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class="form-label required">Kho/ ĐBH</label>

                                        @include('inventory::components.select-warehouse', [
                                            'inputName' => 'warehouse_id',
                                            'selectedWarehose' => $fromWarehouse,
                                        ])

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">

                                        @include('partials.filters.date-filter', [
                                            'inputName' => 'acceptance_date',
                                            'label' => 'Ngày tiêu huỷ',
                                            'inputValue' => $disposal->actual_date,
                                            'required' => true,
                                            // 'disablePastDates' => $last_inventory_date,
                                            'highlightDates' => $highlightDates,
                                        ])
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class=" form-label">Đính kèm tài liệu
                                            <span class="ms-2" data-bs-toggle="tooltip" data-bs-placement="top"
                                                  title="Only jpg, jpeg, png, gif, pdf, csv, docx, xlsx and txt file is supported">
                                                <i class="ki-duotone ki-information fs-7">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                            </span>
                                        </label>

                                        <div class=" mb-2">
                                            @include('partials.upload-file', [
                                             'inputName' => 'document',
                                                    'multiUpload' => true,
                                                    'files' => $disposal->document,


                                            ])
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4" id="selected-product-modal">

                                    <label class=" form-label">{{ trans('Select equipment') }}</label>
                                    <div class="search-box input-group">

                                        @include('inventory::components.selected-product-modal', [
                                        ])

                                    </div>
                                </div>

                            </div>
                            <div class="row mt-5">
                                <div class="col-md-12">
                                    <h5 class="required">Danh sách trang thiết bị</h5>

                                    @include('inventory::components.selected-product-table', [
                                        'products' => $disposal->productTransfers,
                                        'transfer_id' => $disposal->id,
                                    ])


                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-4 fv-row fv-plugins-icon-container">
                                        <label class="form-label">{{ trans('Note') }}</label>
                                        <textarea rows="5" class="form-control"
                                                  name="note">{!! $disposal->note !!}</textarea>
                                    </div>
                                </div>
                            </div>


                            @include('inventory::components.btn-action', [
                                'btnCancelUrl' => route(checkType('disposal.index')),
                                'btnSubmitText' => 'Cập nhật',
                                'btnPrintUrl' => route(checkType('disposal.print'), $disposal->id),
                            ])

                        </div>
                    </div>
                    {!! Form::close() !!}
                </div>
            </section>
        </div>
    </div>
@stop

@section('style-vendor')
    <link href="{{ asset('assets/plugins/custom/datatables/datatables.bundle.css') }}" rel="stylesheet"
          type="text/css"/>
@stop

@section('script-vendor')
    <script src="{{ asset('assets/plugins/custom/datatables/datatables.bundle.js') }}"></script>
@stop

@section('script')

    <script>
        "use strict";
        var ValidatorForm;
        var form = document.getElementById("hand-over-form");
        var submitButton = document.getElementById("submit-btn");
        var transferEdit = {{ $itemAction && $edit ? 'true' : 'false' }};
        var transferEditItems = [];

        var options = {
            fields: {
                reference_no: {
                    validators: {
                        notEmpty: {
                            message: "Trường này là bắt buộc",
                        },
                    },
                },
                warehouse_id: {
                    validators: {
                        notEmpty: {
                            message: "Trường này là bắt buộc",
                        },
                    },
                },
                acceptance_date: {
                    validators: {
                        notEmpty: {
                            message: "Trường này là bắt buộc",
                        },
                    },
                },
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap5({
                    rowSelector: ".fv-row",
                    eleInvalidClass: "",
                    eleValidClass: "",
                }),
            },
        }
        ValidatorForm = FormValidation.formValidation(form, options);

        // Class definition
        var KTTransfer = (function () {
            // Get elements

            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                },
            });

            // Function to handle form submission
            const handleSubmitForm = () => {
                if (submitButton) {
                    submitButton.addEventListener("click", (e) => {
                        e.preventDefault();

                        const files = KTFile.updateFormValidationStatus();
                        console.log(files);
                        handleValidateTTB()

                        if (ValidatorForm) {

                            ValidatorForm.validate().then(function (status) {
                                if (status === "Valid") {

                                    submitButton.setAttribute("data-kt-indicator", "on");
                                    submitButton.disabled = true;
                                    var formData = new FormData(form);

                                    files.forEach(function (file) {

                                        if (file instanceof File) {
                                            formData.append('document[]', file, file
                                                .name);
                                        } else {
                                            formData.append('document_url[]', file
                                                .name);
                                        }
                                    });

                                    let jsonData = {};
                                    $(form).serializeArray().forEach(field => {
                                        jsonData[field.name] = field.value;
                                    });

                                    formData.append('jsonData', JSON.stringify(jsonData));
                                    var action = form.getAttribute("action");
                                    axios
                                        .post(action, formData)
                                        .then(function ({
                                                            data
                                                        }) {
                                            submitButton.disabled = false;
                                            submitButton.setAttribute(
                                                "data-kt-indicator",
                                                "off"
                                            );

                                            if (data.success) {
                                                KTApp.notify({
                                                    title: data.message
                                                })
                                                window.location.reload();

                                            } else {
                                                KTApp.notify({
                                                    icon: 'error',
                                                    title: data.message
                                                })
                                            }
                                        })
                                        .catch(function (error) {
                                            submitButton.disabled = false;
                                            submitButton.setAttribute(
                                                "data-kt-indicator",
                                                "off"
                                            );
                                            KTApp.handleError(error)
                                        });
                                }
                            });
                        }
                    });
                }
            };
            // Public methods
            return {
                init: function () {
                    handleSubmitForm();
                }
            };
        })();

        // On document ready
        KTUtil.onDOMContentLoaded(function () {
            KTTransfer.init();

        });
    </script>
@stop
