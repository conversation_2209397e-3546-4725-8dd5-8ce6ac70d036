<?php

namespace Modules\Inventory\app\Http\Controllers;

use App\Http\Controllers\Api\ApiController;
use App\LaravelFilemanager\Handlers\FileUploadService;
use App\Pos;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Modules\Inventory\app\Constants\ItemAction;
use Modules\Inventory\app\Models\ProductTransferItem;
use Modules\Inventory\app\Models\Transfer;
use Modules\Inventory\app\Models\Warehouse;
use Modules\Inventory\app\Repositories\Item\ItemRepository;
use Modules\Inventory\app\Repositories\Transfer\TransferRepository;
use Yajra\DataTables\Facades\DataTables;

class TransferController extends ApiController
{

    protected $transferRepo;
    protected $itemRepo;

    public function __construct(TransferRepository $transferRepo, ItemRepository $itemRepo)
    {
        parent::__construct();
        $this->transferRepo = $transferRepo;
        $this->itemRepo = $itemRepo;
    }

    public function index(Request $request)
    {
        $from_warehouse_id = $request->input('from_warehouse_id');
        $to_warehouse_id = $request->input('to_warehouse_id');
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        if ($from_warehouse_id)
            $from_warehouse = Warehouse::findOrFail($from_warehouse_id);
        else
            $from_warehouse = null;

        if ($to_warehouse_id)
            $to_warehouse = Warehouse::findOrFail($to_warehouse_id);
        else
            $to_warehouse = null;

        $query = $this->transferRepo->getTransfersByType(ItemAction::ACTION_TRANSFER);

        if ($request->expectsJson()) {



            if ($from_warehouse_id && $to_warehouse_id) {
                $query->where(function ($query) use ($from_warehouse_id, $to_warehouse_id) {
                    $query->where('from_warehouse_id', $from_warehouse_id)
                        ->where('to_warehouse_id', $to_warehouse_id);
                });
            }

            if ($from_warehouse_id) {
                $query->where('from_warehouse_id', $from_warehouse_id);
            }
            if ($to_warehouse_id) {
                $query->where('to_warehouse_id', $to_warehouse_id);
            }

            if ($start_date && $end_date) {
                $query->whereBetween('acceptance_date', [$start_date, $end_date]);
            }

            return DataTables::of($query)
                ->filterColumn('to_warehouse', function ($query, $keyword) {
                    $query->whereHas('toWarehouse', function ($query) use ($keyword) {
                        $query->where('name', 'ilike', "%$keyword%")
                            ->orWhere('address', 'ilike', "%$keyword%");
                    });
                })
                ->filterColumn('from_warehouse', function ($query, $keyword) {
                    $query->whereHas('fromWarehouse', function ($query) use ($keyword) {
                        $query->where('name', 'ilike', "%$keyword%")
                            ->orWhere('address', 'ilike', "%$keyword%");
                    });
                })

                ->editColumn('to_warehouse', function ($row) {
                    return '<a href="' . $row->toWarehouse->routeUrl() . '" >' . $row->toWarehouse->name . ' - ' . $row->toWarehouse->address . '</a>';
                })

                ->editColumn('from_warehouse', function ($row) {
                    return '<a href="' . $row->fromWarehouse->routeUrl() . '" >' . $row->fromWarehouse->name . ' - ' . $row->fromWarehouse->address . '</a>';
                })

                ->editColumn('reference_no', function ($row) {
                    return '<a  href="' . route(checkType('transfers.show'), $row) . '" > ' . $row->reference_no . '</a>';
                })

                ->addColumn('action', function ($row) {
                    $actionBtn = '';

                    $actionBtn .= '<div class="text-end" >
                        <a href="#"
                        class=" btn btn-light btn-active-light-primary btn-flex btn-center btn-sm dropdown_actions"
                        data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                         <i class="ki-duotone ki-setting-3 fs-3">
                             <span class="path1"></span>
                             <span class="path2"></span>
                             <span class="path3"></span>
                             <span class="path4"></span>
                             <span class="path5"></span>
                         </i>
                         <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                         <div data-kt-menu="true" class="dropdown_item menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" user="menu">

                         <div class="menu-item px-3"> <a href="' . route(checkType('transfers.show'), $row->id) . '" class="menu-link px-3"> Xem </a></div>';
                    if ($row->editable && auth()->user()->hasPermission(checkTypePms('transfer.update'))) {
                        $actionBtn .= '<div class="menu-item px-3"> <a href="' . route(checkType('transfers.edit'), $row->id) . '" class="menu-link px-3"> Chỉnh sửa </a></div>';
                    }
                    if (auth()->user()->hasPermission(checkTypePms('transfer.delete'))) {
                        $actionBtn .= '<div class="menu-item px-3">';
                        $actionBtn .= \Form::open(["route" => [checkType("transfers.destroy"), $row->id], "method" => "DELETE", 'id' => 'deleteForm_' . $row->id]);
                        $actionBtn .= '<div data-row-id="' . $row->id . '"  data-row-name=\'' . $row->reference_no . '\' class="menu-link px-3 delete-row">Xóa</div>';
                        $actionBtn .= \Form::close();
                    }
                    $actionBtn .= '</div></div>';
                    return $actionBtn;
                })
                ->rawColumns(['action', 'reference_no', 'to_warehouse', 'from_warehouse'])
                ->make(true);
        }

        $lims_warehouse_list = Warehouse::select('name', 'id')->where('is_active', true)->get();
        return view('inventory::backend.transfer.index', compact('start_date', 'end_date', 'from_warehouse', 'to_warehouse', 'lims_warehouse_list'));
    }

    public function create()
    {
        $last_inventory_date = $this->transferRepo->getLatestActualDate();

        return view('inventory::backend.transfer.create', compact('last_inventory_date'));
    }

    public function storeTransfer(Request $request, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);

        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $allInputs['document'] = $documents;

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            // Gọi phương thức createHandover trong repository để tạo biên bản bàn giao
            $transfer = $this->transferRepo->createTransfer($allInputs);
            return $this->respondWithSuccess([
                'message' => 'Thêm mới thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function edit($id, $edit = true)
    {
        $transfer = Transfer::with('toWarehouse', 'fromWarehouse', 'productTransfers.product.unit:id,unit_name', 'productTransfers.tem:id,code,branch_code')->findOrFail($id);

        $items = ProductTransferItem::with(['product', 'warehouse', 'state', 'latestCheck'])
            ->where('warehouse_id', $transfer->from_warehouse_id)->get();

        $items_qty = $items->pluck('qty', 'product_id')->toArray();

        $last_inventory_date = $this->transferRepo->getLatestActualDate();

        $highlightDates = $this->itemRepo->getProductItemDates($transfer->fromWarehouse->id);

        return view('inventory::backend.transfer.edit', compact('items_qty', 'transfer', 'edit', 'last_inventory_date', 'highlightDates'));
    }

    public function show($id)
    {
        return $this->edit($id, false);
    }

    public function updateTransfer(Request $request, $id, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);

        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $documentUrls = $request->input('document_url', []);

        $allInputs['document'] = array_unique(array_merge($documents, $documentUrls));

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            // Gọi phương thức createHandover trong repository để tạo biên bản bàn giao
            $transfer = $this->transferRepo->updateTransfer($allInputs, $id);

            session()->flash('success', 'Cập nhật điều chuyển thành công!');

            return $this->respondWithSuccess([
                'message' => 'Thêm mới thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function destroy($id)
    {
        $data = Transfer::find($id);

        if ($data) {
            if ($data->editable) {
                $data->delete();
                return response()->json(['success' => true, 'message' => 'Đã xóa thành công']);
            } else {
                return response()->json(['success' => false, 'message' => 'Không thể xóa bản ghi']);
            }
        } else {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy bản ghi']);
        }
    }

    public function getHandover(Request $request)
    {

        $warehouseId = $request->input('warehouse_id', 0);
        $type = $request->input('type', ItemAction::ACTION_HANDOVER);
        $status = $request->input('status', Transfer::STATUS_PENDING);
        $perPage = $request->input('perPage', $this->pageSize);

        $handoverDate = $request->input('start_date');
        $acceptanceDate = $request->input('end_date');

        if ($warehouseId)
            $warehouses = Warehouse::findOrFail($warehouseId)->formattedDetails;
        else
            $warehouses = null;

        $transfersQuery = Transfer::query();

        if ($warehouseId) {
            $transfersQuery->where(function ($query) use ($warehouseId) {
                $query->where('from_warehouse_id', $warehouseId)
                    ->orWhere('to_warehouse_id', $warehouseId);
            });
        }

        if ($type) {
            $transfersQuery->where('type', $type);
        }

        if ($status) {
            $transfersQuery->where('status', $status);
        }

        if ($handoverDate && $acceptanceDate) {

            $transfersQuery->whereBetween('handover_date', [$handoverDate, $acceptanceDate])
                ->whereBetween('acceptance_date', [$handoverDate, $acceptanceDate]);
        }

        $transfersQuery->orderBy('id', 'desc');
        $transfers = $transfersQuery->paginate($perPage)->withQueryString();


        $filterWarehouse = [
            'id' => $warehouseId,
            'text' => $warehouses,
        ];


        return view('inventory::backend.transfer.handover.index', compact(
            'transfers',
            'warehouseId',
            'handoverDate',
            'acceptanceDate',
            'type',
            'status',
            'filterWarehouse'
        ));
    }

    public function createHandover(Request $request)
    {
        return view('inventory::backend.transfer.handover.create');
    }

    public function storeHandover(Request $request, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $allInputs['document'] = $documents;

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            // Gọi phương thức createHandover trong repository để tạo biên bản bàn giao
            $transfer = $this->transferRepo->createHandover($allInputs);
            return $this->respondWithSuccess([
                'message' => 'Thêm mới thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function editHandover($id)
    {

        $transfer = Transfer::with([
            'productTransfers' => function ($query) {
                $query->with('product');
            }
        ])->findOrFail($id);

        $toWarehouse = [
            'id' => $transfer->toWarehouse->id,
            'text' => $transfer->toWarehouse->formattedDetails,
        ];

        return view('inventory::backend.transfer.handover.edit', compact('transfer', 'toWarehouse'));
    }

    public function updateHandover(Request $request, $id, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        unset($allInputs['document_url']);

        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $documentUrls = $request->input('document_url', []);

        $allInputs['document'] = array_unique(array_merge($documents, $documentUrls));

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            $transfer = $this->transferRepo->updateHandover($allInputs, $id);

            return $this->respondWithSuccess([
                'message' => 'Cập nhật thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function destroyHandover($id)
    {
        try {
            \DB::transaction(function () use ($id) {
                $transfer = Transfer::findOrFail($id);
                $transfer->delete();
            });
            return redirect()->route('handover.index')->with('success', 'Đã xoá thành công.');
        } catch (\Exception $e) {
            return redirect()->route('handover.index')->with('error', 'Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function getDisposal(Request $request)
    {
        $handoverDate = $request->input('start_date');
        $acceptanceDate = $request->input('end_date');
        $warehouseId = $request->input('warehouse_id');

        if ($request->expectsJson()) {

            $data = $this->transferRepo->getTransfersByType(ItemAction::ACTION_DISPOSE);

            if ($warehouseId) {
                $data->where('from_warehouse_id', $warehouseId);
            }

            if ($handoverDate && $acceptanceDate) {
                $data->whereBetween('acceptance_date', [$handoverDate, $acceptanceDate]);
            }

            $type = $request->input('type');


            return DataTables::of($data)
                ->filterColumn('from_warehouse', function ($query, $keyword) {
                    $query->whereHas('fromWarehouse', function ($query) use ($keyword) {
                        $query->where('name', 'ilike', "%$keyword%")
                            ->orWhere('address', 'ilike', "%$keyword%");
                    });
                })
                ->editColumn('reference_no', function ($row) {
                    return '<a href="' . route(checkType('disposal.show'), $row) . '" > ' . $row->reference_no . '</a>';
                })
                ->editColumn('from_warehouse', function ($row) {
                    return '<a href="' . $row->fromWarehouse->routeUrl() . '" >' . $row->fromWarehouse->name . ' - ' . $row->fromWarehouse->address . '</a>';
                })
                ->addColumn('action', function ($row) {
                    $actionBtn = '';

                    $actionBtn .= '<div class="text-end" >
                    <a href="#"
                    class=" btn btn-light btn-active-light-primary btn-flex btn-center btn-sm dropdown_actions"
                    data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                     <i class="ki-duotone ki-setting-3 fs-3">
                         <span class="path1"></span>
                         <span class="path2"></span>
                         <span class="path3"></span>
                         <span class="path4"></span>
                         <span class="path5"></span>
                     </i>
                     <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                     <div data-kt-menu="true" class="dropdown_item menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" user="menu">
                     <div class="menu-item px-3"> <a href="' . route(checkType('disposal.show'), $row->id) . '" class="menu-link px-3"> Xem </a></div>';
                    if ($row->editable && auth()->user()->hasPermission(checkTypePms('dispose.update'))) {
                        $actionBtn .= '<div class="menu-item px-3"> <a href="' . route(checkType('disposal.edit'), $row->id) . '" class="menu-link px-3 open-EditWarehouseDialog"> Chỉnh sửa </a></div>';
                    }
                    if (auth()->user()->hasPermission(checkTypePms('dispose.delete'))) {
                        $actionBtn .= '<div class="menu-item px-3">';
                        $actionBtn .= \Form::open(["route" => [checkType("disposal.destroy"), $row->id], "method" => "DELETE", 'id' => 'deleteForm_' . $row->id]);
                        $actionBtn .= '<div data-row-id="' . $row->id . '"  data-row-name=\'' . $row->reference_no . '\' class="menu-link px-3 delete-row">Xóa</div>';
                        $actionBtn .= \Form::close();
                    }
                    $actionBtn .= '</div></div>';
                    return $actionBtn;
                })
                ->rawColumns(['action', 'reference_no','from_warehouse'])
                ->make(true);
        }

        if ($warehouseId)
            $warehouse = Warehouse::findOrFail($warehouseId);
        else
            $warehouse = null;

        return view('inventory::backend.transfer.disposal.index', [
            'handoverDate' => $handoverDate,
            'acceptanceDate' => $acceptanceDate,
            'warehouse' => $warehouse
        ]);
    }

    public function destroyDisposal($id)
    {
        $data = Transfer::find($id);

        if ($data) {
            if ($data->editable) {
                $data->delete();
                return response()->json(['success' => true, 'message' => 'Đã xóa thành công']);
            } else {
                return response()->json(['success' => false, 'message' => 'Không thể xóa bản ghi']);
            }
        } else {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy bản ghi']);
        }
    }

    public function createDisposal(Request $request)
    {
        $last_inventory_date = $this->transferRepo->getLatestActualDate();

        return view('inventory::backend.transfer.disposal.create', [
            'last_inventory_date' => $last_inventory_date
        ]);
    }

    public function storeDisposal(Request $request, FileUploadService $fileUploadService)
    {

        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $allInputs['document'] = $documents;

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            // Gọi phương thức createHandover trong repository để tạo biên bản bàn giao
            $transfer = $this->transferRepo->createDisposal($allInputs);

            return $this->respondWithSuccess([
                'message' => 'Thêm mới thành công',
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function editDisposal($id, $edit = true)
    {
        $disposal = Transfer::with([
            'productTransfers' => function ($query) {
                $query->with('product', 'fromWarehouse', 'toWarehouse');
            }
        ])->findOrFail($id);

        $fromWarehouse = [
            'id' => $disposal->fromWarehouse->id,
            'text' => $disposal->fromWarehouse->formattedDetails,
        ];

        $items = ProductTransferItem::with(['product', 'warehouse', 'state', 'latestCheck'])
            ->where('warehouse_id', $disposal->to_warehouse_id)->get();

        $items_qty = $items->pluck('qty', 'product_id')->toArray();

        $last_inventory_date = $this->transferRepo->getLatestActualDate();

        $highlightDates = $this->itemRepo->getProductItemDates($disposal->fromWarehouse->id);

        return view('inventory::backend.transfer.disposal.edit', compact('items_qty', 'disposal', 'fromWarehouse', 'edit', 'last_inventory_date', 'highlightDates'));
    }

    public function showDisposal($id)
    {
        return $this->editDisposal($id, false);
    }

    public function updateDisposal(Request $request, $id, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        unset($allInputs['document_url']);

        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $documentUrls = $request->input('document_url', []);

        $allInputs['document'] = array_unique(array_merge($documents, $documentUrls));

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            $transfer = $this->transferRepo->updateDisposal($allInputs, $id);

            return $this->respondWithSuccess([
                'message' => 'Cập nhật thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function approveDisposal($transferId)
    {
        try {
            $transfer = $this->transferRepo->approveDisposal($transferId);
            return response()->json([
                'message' => 'Disposal approved successfully',
                'transfer' => $transfer,
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Đã có lỗi xảy ra: ' . $e->getMessage()], 500);
        }
    }


    public function getRecovery(Request $request)
    {
        $handoverDate = $request->input('start_date');
        $acceptanceDate = $request->input('end_date');
        $warehouseId = $request->input('warehouse_id');

        if ($request->expectsJson()) {

            $data = Transfer::with('fromWarehouse', 'toWarehouse', 'productTransfers')
                ->where('type', ItemAction::ACTION_RECOVER);

            if ($handoverDate && $acceptanceDate) {
                $data->whereBetween('acceptance_date', [$handoverDate, $acceptanceDate]);
            }
            if ($warehouseId) {
                $data->where('to_warehouse_id', $warehouseId);
            }

            return DataTables::of($data)
                ->filterColumn('reference_no', function ($query, $keyword) {
                    $query->where('reference_no', 'ilike', "%$keyword%");
                })
                ->editColumn('reference_no', function ($row) {
                    return '<a href="' . route(checkType('recovery.edit'), $row) . '" > ' . $row->reference_no . '</a>';
                })
                ->addColumn('action', function ($row) {
                    $actionBtn = '';
                    if (auth()->user()->hasPermission(checkTypePms('retrieve.update'))) {
                        $actionBtn .= '<div class="text-end" >
                    <a href="#"
                    class=" btn btn-light btn-active-light-primary btn-flex btn-center btn-sm dropdown_actions"
                    data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                     <i class="ki-duotone ki-setting-3 fs-3">
                         <span class="path1"></span>
                         <span class="path2"></span>
                         <span class="path3"></span>
                         <span class="path4"></span>
                         <span class="path5"></span>
                     </i>
                     <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                     <div data-kt-menu="true" class="dropdown_item menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" user="menu">
                     <div class="menu-item px-3"> <a href="' . route(checkType('recovery.edit'), $row->id) . '" class="menu-link px-3 open-EditWarehouseDialog"> Chỉnh sửa </a></div>';
                    }
                    if (auth()->user()->hasPermission(checkTypePms('retrieve.delete'))) {
                        $actionBtn .= '<div class="menu-item px-3">';
                        $actionBtn .= \Form::open(["route" => [checkType("recovery.destroy"), $row->id], "method" => "DELETE", 'id' => 'deleteForm_' . $row->id]);
                        $actionBtn .= '<div data-row-id="' . $row->id . '"  data-row-name=\'' . $row->reference_no . '\' class="menu-link px-3 delete-row">Xóa</div>';
                        $actionBtn .= \Form::close();
                    }
                    $actionBtn .= '</div></div>';
                    return $actionBtn;
                })
                ->rawColumns(['action', 'reference_no'])
                ->make(true);
        }

        if ($warehouseId)
            $warehouse = Warehouse::findOrFail($warehouseId);
        else
            $warehouse = null;

        return view('inventory::backend.transfer.recovery.index', [
            'handoverDate' => $handoverDate,
            'acceptanceDate' => $acceptanceDate,
            'warehouse' => $warehouse
        ]);
    }

    public function destroyRecovery($id)
    {
        $data = Transfer::find($id);
        if ($data) {
            $data->delete();
            return response()->json(['success' => true, 'message' => 'Đã xóa thành công']);
        } else {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy bản ghi']);
        }
    }

    public function createRecovery(Request $request)
    {
        return view('inventory::backend.transfer.recovery.create');
    }

    public function storeRecovery(Request $request, FileUploadService $fileUploadService)
    {

        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $allInputs['document'] = $documents;

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            // Gọi phương thức createHandover trong repository để tạo biên bản bàn giao
            $transfer = $this->transferRepo->createRecovery($allInputs);
            return $this->respondWithSuccess([
                'message' => 'Thêm mới thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function editRecovery($id)
    {
        $recovery = Transfer::with([
            'productTransfers' => function ($query) {
                $query->with('product');
            }
        ])->findOrFail($id);

        $toWarehouse = [
            'id' => $recovery->toWarehouse->id,
            'text' => $recovery->toWarehouse->formattedDetails,
        ];

        $fromWarehouse = [
            'id' => $recovery->fromWarehouse->id,
            'text' => $recovery->fromWarehouse->formattedDetails,
        ];

        return view('inventory::backend.transfer.recovery.edit', compact('recovery', 'toWarehouse', 'fromWarehouse'));
    }

    public function updateRecovery(Request $request, $id, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        unset($allInputs['document_url']);

        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $documentUrls = $request->input('document_url', []);

        $allInputs['document'] = array_unique(array_merge($documents, $documentUrls));

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            $transfer = $this->transferRepo->updateRecovery($allInputs, $id);

            return $this->respondWithSuccess([
                'message' => 'Cập nhật thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }


    public function approveRecovery($transferId)
    {
        try {
            $transfer = $this->transferRepo->approveRecovery($transferId);
            return response()->json([
                'message' => 'Recovery approved successfully',
                'transfer' => $transfer,
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Đã có lỗi xảy ra: ' . $e->getMessage()], 500);
        }
    }

    public function getInventory(Request $request)
    {
        $type = $request->input('type');
        $to_warehouse_id = $request->input('to_warehouse_id');

        $query = $this->transferRepo->getTransfersByType(ItemAction::ACTION_INVENTORY);

        if ($to_warehouse_id) {
            $query->where('to_warehouse_id', $to_warehouse_id);
        }

        if ($request->expectsJson()) {

            return DataTables::of($query)
                ->filterColumn('to_warehouse', function ($query, $keyword) {
                    $query->whereHas('toWarehouse', function ($query) use ($keyword) {
                        $query->where('name', 'ilike', "%$keyword%")
                            ->orWhere('address', 'ilike', "%$keyword%");
                    });
                })
                ->editColumn('reference_no', function ($row) {
                    return '<a href="' . route(checkType('inventory.show'), $row) . '" > ' . $row->reference_no . '</a>';
                })
                ->editColumn('to_warehouse', function ($row) {
                    return '<a href="' . $row->toWarehouse->routeUrl() . '" >' . $row->toWarehouse->name . ' - ' . $row->toWarehouse->address . '</a>';
                })
                ->addColumn('action', function ($row) {
                    $actionBtn = '';

                    $actionBtn .= '<div class="text-end" >
                    <a href="#"
                    class=" btn btn-light btn-active-light-primary btn-flex btn-center btn-sm dropdown_actions"
                    data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                     <i class="ki-duotone ki-setting-3 fs-3">
                         <span class="path1"></span>
                         <span class="path2"></span>
                         <span class="path3"></span>
                         <span class="path4"></span>
                         <span class="path5"></span>
                     </i>
                     <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                     <div data-kt-menu="true" class="dropdown_item menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" user="menu">
                     <div class="menu-item px-3"> <a href="' . route(checkType('inventory.show'), $row->id) . '" class="menu-link px-3"> Xem </a></div>';
                    if ($row->editable && auth()->user()->hasPermission(checkTypePms('stock_in.update'))) {
                        $actionBtn .= '<div class="menu-item px-3"> <a href="' . route(checkType('inventory.edit'), $row->id) . '" class="menu-link px-3 open-EditWarehouseDialog"> Chỉnh sửa </a></div>';
                    }
                    if (auth()->user()->hasPermission(checkTypePms('stock_in.delete'))) {
                        $actionBtn .= '<div class="menu-item px-3">';
                        $actionBtn .= \Form::open(["route" => [checkType("inventory.destroy"), $row->id], "method" => "DELETE", 'id' => 'deleteForm_' . $row->id]);
                        $actionBtn .= '<div data-row-id="' . $row->id . '"  data-row-name=\'' . $row->reference_no . '\' class="menu-link px-3 delete-row">Xóa</div>';
                        $actionBtn .= \Form::close();
                    }
                    $actionBtn .= '</div></div>';
                    return $actionBtn;
                })
                //                ->editColumn('handover_date', function ($row) {
                //                    return formatDate($row->handover_date);
                //                })
                ->rawColumns(['reference_no', 'action','to_warehouse'])
                ->make(true);
        }

        $warehouseId = $request->input('warehouse_id', 0);
        $handoverDate = $request->input('handover_date', date("Y-m-d", strtotime('-1 year')));
        $acceptanceDate = $request->input('acceptance_date', date("Y-m-d"));

        if ($warehouseId)
            $warehouse = Warehouse::findOrFail($warehouseId);
        else
            $warehouse = null;

        return view('inventory::backend.transfer.inventory.index', [
            'handoverDate' => $handoverDate,
            'acceptanceDate' => $acceptanceDate,
            'warehouse' => $warehouse,
            'type' => $type
        ]);
    }

    public function showInventory(Request $request, $id)
    {

        return $this->editInventory($id, false);
    }

    public function storeInventory(Request $request, FileUploadService $fileUploadService)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $allInputs['document'] = $documents;

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            // Gọi phương thức createHandover trong repository để tạo biên bản bàn giao
            $transfer = $this->transferRepo->createInventory($allInputs);
            return $this->respondWithSuccess([
                'message' => 'Thêm mới thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function editInventory($id, $edit = true)
    {
        $inventory = Transfer::with([
            'productTransfers' => function ($query) {
                $query->with('product');
            }
        ])->findOrFail($id);

        $toWarehouse = [
            'id' => $inventory->toWarehouse->id,
            'text' => $inventory->toWarehouse->formattedDetails,
        ];

        return view('inventory::backend.transfer.inventory.edit', compact('inventory', 'toWarehouse', 'edit'));
    }

    public function print($id)
    {
        $data = Transfer::with(
            [
                'productTransfers' => function ($query) {
                    $query->with(['product', 'condition']);
                },
                'toWarehouse',
                'supplier'
            ]
        )->findOrFail($id);

        $warehouse = $data->toWarehouse;

        $template = '';

        switch ($data->type) {
            case ItemAction::ACTION_INVENTORY:

                if ($warehouse->assignable_type == Pos::class) {
                    $template = 'bb_ban_giao';
                } else {
                    $template = 'nhap_moi_tu_ncc';
                }

                break;
            case ItemAction::ACTION_TRANSFER:
                $template = 'bb_ban_giao';
                break;
            case ItemAction::ACTION_DISPOSE:
                $template = 'bien_ban_tieu_huy';
                break;
            default:
                break;
        }



        $pdf = PDF::loadView('inventory::backend.transfer.inventory.' . $template . '', compact('data', 'warehouse'));
        return $pdf->stream('inventory.pdf');
    }

    public function updateInventory(Request $request, FileUploadService $fileUploadService, $id)
    {
        $allFiles = $request->allFiles();
        $allInputs = $request->all();
        $jsonData = json_decode($request->input('jsonData'), true);
        unset($allInputs['jsonData']);
        unset($allInputs['product_items']);
        unset($allInputs['document_url']);

        $documents = [];
        // Xử lý tệp tài liệu
        if (isset($allFiles['document'])) {
            foreach ($allFiles['document'] as $file) {
                $filePath = $fileUploadService->uploadFile($file, false, false);
                $documents[] = $filePath;
            }
        }
        $documentUrls = $request->input('document_url', []);

        $allInputs['document'] = array_unique(array_merge($documents, $documentUrls));

        $products = [];
        foreach ($jsonData as $key => $value) {
            if (preg_match('/^products\[(\d+)\]\.(.+)$/', $key, $matches)) {
                $index = $matches[1];
                $field = $matches[2];
                $products[$index][$field] = $value;
            }
        }
        $allInputs['products'] = $products;

        try {
            $transfer = $this->transferRepo->updateInventory($allInputs, $id);

            return $this->respondWithSuccess([
                'message' => 'Cập nhật thành công',
                'transfer' => $transfer
            ]);
        } catch (\Exception $e) {
            return $this->errorInternalError('Đã có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function createInventory(Request $request)
    {
        return view('inventory::backend.transfer.inventory.create');
    }


    public function destroyInventory($id)
    {
        $data = Transfer::find($id);

        if ($data) {
            if ($data->editable) {
                $data->delete();
                return response()->json(['success' => true, 'message' => 'Đã xóa thành công']);
            } else {
                return response()->json(['success' => false, 'message' => 'Không thể xóa bản ghi']);
            }
        } else {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy bản ghi']);
        }
    }

    public function approveInventory($transferId)
    {
        try {
            $transfer = $this->transferRepo->approveInventory($transferId);
            return response()->json([
                'message' => 'Inventory approved successfully',
                'transfer' => $transfer,
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Đã có lỗi xảy ra: ' . $e->getMessage()], 500);
        }
    }
}
