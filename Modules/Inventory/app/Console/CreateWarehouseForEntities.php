<?php
namespace Modules\Inventory\app\Console;

use App\Branch;
use App\Pos;
use Illuminate\Console\Command;
use Modules\Inventory\app\Models\Supplier;
use Modules\Inventory\app\Models\Warehouse;

class CreateWarehouseForEntities extends Command
{
    /**
     * php artisan warehouse:create-for-entities
     */
    protected $signature = 'warehouse:create-for-entities';
    protected $description = 'Create a warehouse for each pos, branch, and supplier that does not already have one';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->createWarehousesForPos();
        $this->createWarehousesForBranches();
        $this->createWarehousesForSuppliers();

        $this->info('All entities processed.');
    }

    protected function createWarehousesForPos()
    {
        $poss = Pos::doesntHave('warehouse')->get();

        foreach ($poss as $pos) {
            Warehouse::create([
                'name' => $pos->posCode,
                'code' => $pos->posCode,
                'address' => $pos->address,
                'assignable_code' => $pos->posCode,
                'assignable_type' => Pos::class,
                'removable' => false,
                'description' => 'Đồng bộ tự động cho ĐBH',
                'is_active' => true,
            ]);
            $this->info("Warehouse created for POS: {$pos->posCode}");
        }
    }

    protected function createWarehousesForBranches()
    {
        $branches = Branch::doesntHave('warehouse')->get();

        foreach ($branches as $branch) {
            Warehouse::create([
                'name' => $branch->name,
                'code' => $branch->code,
                'address' => $branch->name, // Assuming 'name' is used as 'address' for simplification
                'assignable_code' => $branch->code,
                'assignable_type' => Branch::class,
                'removable' => false,
                'description' => 'Đồng bộ tự động cho Chi nhánh',
                'is_active' => true,
            ]);
            $this->info("Warehouse created for Branch: {$branch->code}");
        }
    }

    protected function createWarehousesForSuppliers()
    {
        $suppliers = Supplier::doesntHave('warehouse')->get();

        foreach ($suppliers as $supplier) {
            Warehouse::create([
                'name' => $supplier->name,
                'code' => $supplier->id,
                'address' => $supplier->address,
                'assignable_id' => $supplier->id, // Assuming VAT number is unique and used as code
                'assignable_type' => Supplier::class,
                'removable' => false,
                'description' => 'Đồng bộ tự động cho Nhà cung cấp',
                'is_active' => true,
            ]);
            $this->info("Warehouse created for Supplier: {$supplier->name}");
        }
    }
}
