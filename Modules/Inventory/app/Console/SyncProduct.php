<?php

namespace Modules\Inventory\app\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\Inventory\app\Models\Supplier;
use Modules\Inventory\app\Models\Unit;

class SyncProduct extends Command
{
    /**
     * php artisan sync:product
     */
    protected $signature = 'sync:product';
    protected $description = 'Đồng bộ dữ liệu từ bảng loai_ttb của DB_CONNECTION_SYNC vào bảng products';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $synCodesList = config('inventory.products_syn',[]);

        $loaiTTBs = DB::connection('sqlsrv')->table('danh_muc.loai_ttb')->whereIn('ma',$synCodesList)->get();

        $units = Unit::all()->keyBy('unit_name');

        $suppliers = Supplier::whereIn('type', [Supplier::TYPE_VIETLOTT, Supplier::TYPE_BERJAYA])
            ->get()
            ->keyBy('name');
        // Kết nối tới cơ sở dữ liệu mặc định
        foreach ($loaiTTBs as $loaiTTB) {
            $unit = $units[$loaiTTB->don_vi_tinh] ?? null;
            $unit_id = $unit ? $unit->id : null;
            $supplier = $loaiTTB->berjaya_cung_cap ? $suppliers['Berjaya'] : $suppliers['Vietlott'];
            $supplier_id = $supplier ? $supplier->id : null;

            DB::connection('pgsql')->table('products')->updateOrInsert(
                ['code' => $loaiTTB->ma],
                [
                    'name' => $loaiTTB->ten,
                    'is_imei' => $loaiTTB->co_ma_tem ? 1 : 0,
                    'supplier_id' => $supplier_id,
                    'unit_id' => $unit_id,
                    'month_warranty' => $loaiTTB->so_thang_bao_hanh,
                    'month_allocation' => $loaiTTB->so_thang_phan_bo,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }

        $this->info('Đồng bộ dữ liệu từ bảng loai_ttb thành công!');
    }
}
