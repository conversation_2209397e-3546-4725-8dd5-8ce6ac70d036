<?php

namespace Modules\Inventory\app\Repositories\Item;

use App\Agency;
use App\Branch;
use App\Pos;
use App\Repositories\Pos\PosRepository;
use App\Repositories\User\UserRepository;
use App\Support\Enum\Perms;
use Modules\Inventory\app\Constants\ItemAction;
use Modules\Inventory\app\Models\EquipmentStatusList;
use Modules\Inventory\app\Models\ProductItem;
use Modules\Inventory\app\Models\ProductTransfer;
use Modules\Inventory\app\Models\ProductTransferItem;
use Modules\Inventory\app\Models\Warehouse;
use Modules\Inventory\app\Services\ProductCodeService;

class EloquentItem implements ItemRepository
{
    private $posRepo;
    private $userRepo;

    public function __construct(UserRepository $userRepo, PosRepository $posRepo)
    {
        $this->posRepo = $posRepo;
        $this->userRepo = $userRepo;
    }

    /**
     * Create a new product item from a transfer.
     *
     * @param ProductTransfer $transfer The transfer object from which to create the product item.
     * @return ProductItem|null The newly created product item or null on failure.
     */
    public function createProductItem(ProductTransfer $transfer)
    {

        $newItem = null;
        try {
            switch ($transfer->action) {
                case ItemAction::ACTION_HANDOVER:
                case ItemAction::ACTION_INVENTORY:
                    if (!$transfer->item_id) {
                        $productCode = ProductCodeService::generateUniqueProductCode('PROD');

                        $newItem = ProductItem::create([
                            'code' => $productCode,
                            'product_id' => $transfer->product_id,
                            'tem_id' => $transfer->tem_id,
                            'supplier_id' => $transfer->supplier_id,
                            'warehouse_id' => $transfer->to_warehouse_id,
                            'status' => ProductItem::STATUS_ACTIVE,
                            'qty' => $transfer->to_actual_qty,
                        ]);
                        if ($newItem) {
                            $transfer->item_id = $newItem->id;
                            $transfer->save();
                        }
                    }
                    break;
            }
            return $newItem;
        } catch (\Exception $e) {
            report($e);
            return null;
        }
    }

    /**
     * Handle the deletion of a ProductItem related to a ProductTransfer.
     *
     * @param ProductTransfer $transfer
     */
    public function deleteProductItem(ProductTransfer $transfer)
    {
        if ($transfer->item_id) {
            $item = ProductItem::find($transfer->item_id);
            if ($item) {
                switch ($transfer->action) {
                    case ItemAction::ACTION_INVENTORY:
                    case ItemAction::ACTION_HANDOVER:
                        $item->status = ProductItem::STATUS_INACTIVE;
                        $item->save();
                        break;
                }
                \Log::info("Handled deletion for ProductItem ID: {$item->id} after ProductTransfer ID: {$transfer->id} was deleted.");
            }
        }
    }


    public function queryItemsByWarehousePosCode($posCode)
    {
        $pos = $this->posRepo->findOrFail($posCode);

        if (!$pos) {
            return ProductTransferItem::whereRaw('1 = 0');
        }

        $warehouse = Warehouse::where('assignable_code', $posCode)->first();
        if (!$warehouse) {
            return ProductTransferItem::whereRaw('1 = 0');
        }

        return ProductTransferItem::with(['product', 'warehouse', 'state', 'latestCheck', 'item'])
            ->where('warehouse_id', $warehouse->id);
    }

    public function queryItemsByWarehouseId($id)
    {
        $warehouse = Warehouse::find($id);
        if (!$warehouse) {
            return ProductTransferItem::whereRaw('1 = 0');
        }

        return ProductTransferItem::with(['product', 'warehouse', 'state', 'latestCheck', 'item'])
            ->where('warehouse_id', $warehouse->id)
            ->where('qty', '>', 0);
    }

    public function queryItemsByUser($user = null)
    {
        if ($user === null) {
            $user = auth()->user();
        }

        $query = ProductTransferItem::query();

        if ($user->hasPermission(Perms::BRANCH_MANAGER)) {
            return $query;
        }

        $accessiblePosIdsQuery = $this->posRepo->getAccessiblePosQuery($user)->select('posCode');
        $accessibleBranchCodes = $this->userRepo->getAccessibleBranchIds($user);
        $accessibleAgencyCodes = $this->userRepo->getAccessibleAgencyIds($user);

        $query->whereHas('warehouse', function ($q) use ($accessiblePosIdsQuery, $accessibleBranchCodes, $accessibleAgencyCodes, $user) {
            $q->where(function ($subQuery) use ($accessiblePosIdsQuery, $accessibleBranchCodes, $accessibleAgencyCodes, $user) {

                if ($user->hasPermission(Perms::POS_OWNER)) {
                    $subQuery->orWhere(function ($posQuery) use ($accessiblePosIdsQuery) {
                        $posQuery->where('assignable_type', Pos::class)
                            ->whereIn('assignable_code', $accessiblePosIdsQuery);
                    });
                }
                if ($user->hasPermission(Perms::POS_AGENT)) {
                    $subQuery->orWhere(function ($agencyQuery) use ($accessibleAgencyCodes) {
                        $agencyQuery->where('assignable_type', Agency::class)
                            ->whereIn('assignable_code', $accessibleAgencyCodes);
                    });
                }

                if ($user->hasPermission(Perms::BRANCH_OWNER)) {
                    $subQuery->orWhere(function ($branchQuery) use ($accessibleBranchCodes) {
                        $branchQuery->where('assignable_type', Branch::class)
                            ->whereIn('assignable_code', $accessibleBranchCodes);
                    });
                }
            });
        });



        return $query;
    }

    public function getProductItems($request = null, $warehouseId, $status, $productType)
    {

        $query = $this->queryItemsByUser()->with(['product', 'product.unit', 'tem', 'state', 'warehouse']);

        $query = $this->filterByRequest($query, $request);

        $query = $this->filterProductItemsByRequest($query, $warehouseId, $status, $productType, $request);

        return $query;

    }

    public function filterProductItemsByRequest($query, $warehouseId, $status, $productType, $request)
    {
        return $query->when($warehouseId, function ($query, $warehouseId) {
            return $query->where('warehouse_id', $warehouseId);
        })
            ->when($status, function ($query, $status) {
                $query->where(function ($query) use ($status) {
                    $query->whereHas('state.status', function ($q) use ($status) {
                        $q->where('type', $status);
                    });
                    if ($status == EquipmentStatusList::TYPE_GUARANTEED) {
                        $query->orWhereNull('state_id');
                    }
                });
            })

            ->when($productType, function ($query, $productType) {
                return $query->where('product_id', $productType);
            })
            ->where('qty', '>', 0);
    }


    public function getItemByIds($product_id, $warehouse_id, $tem_id = null, $item_id = null)
    {

        if ($item_id !== null) {
            return $this->getItemById($item_id);
        }

        $query = ProductTransferItem::query();
        $query->where('product_id', $product_id)
            ->where('warehouse_id', $warehouse_id);

        if ($tem_id) {
            $query->where('tem_id', $tem_id);
        }

        return $query->first();

        /*  $cacheKey = CacheKey::itemDetail($product_id, $warehouse_id, $tem_id);

          return Cache::tags([CacheKey::PRODUCT_TRANSFERS_TAG])->remember($cacheKey, CacheDuration::getShortDuration(), function () use ($product_id, $warehouse_id, $tem_id) {
              $query = ProductTransferItem::query();
              $query->where('product_id', $product_id)
                  ->where('warehouse_id', $warehouse_id);

              if ($tem_id) {
                  $query->where('tem_id', $tem_id);
              }

              return $query->first();
          });*/
    }
    public function getItemById($item_id)
    {
        return ProductTransferItem::with('product', 'tem', 'item', 'warehouse')->orderBy('latest_actual_date', 'DESC')->firstWhere('item_id', $item_id);
    }

    /**
     * Filters ProductItem based on the provided Request object.
     *
     * @param  $query The query to filter.
     * @param  $request The request object containing filter parameters.
     * @return ProductItem The filtered ProductItem.
     */
    public function filterByRequest($query, $request)
    {
        return $query->where(function ($query) use ($request) {
            if ($request->branch) {
                $this->applyBranchFilter($query, $request->branch);
            }

            if ($request->province) {
                $this->applyProvinceFilter($query, $request->province);
            }

            if ($request->agency) {
                $this->applyAgencyFilter($query, $request->agency);
            }

            if ($request->pos) {
                $this->applyPosFilter($query, $request->pos);
            }
        });
    }

    public function getProductItemDates($warehouse_id)
    {
        $data = $this->queryItemsByWarehouseId($warehouse_id)->pluck('latest_actual_date');

        return $data;
    }

    private function applyBranchFilter($query, $branch)
    {
        $query->where(function ($q) use ($branch) {
            $q->whereHas('warehouse.pos.branch', function ($q) use ($branch) {
                $q->where('code', $branch);
            })
                ->orWhereHas('warehouse.branch', function ($q) use ($branch) {
                    $q->where('code', $branch);
                })
                ->orWhereHas('warehouse.agency.branch', function ($q) use ($branch) {
                    $q->where('code', $branch);
                });
        });
    }

    private function applyProvinceFilter($query, $province)
    {
        $query->whereHas('warehouse.pos.province', function ($q) use ($province) {
            $q->where('locationCode', $province);
        });
    }

    private function applyAgencyFilter($query, $agency)
    {
        $query->whereHas('warehouse.pos.agency', function ($q) use ($agency) {
            $q->where('agencyCode', $agency);
        });
    }

    private function applyPosFilter($query, $pos)
    {
        $query->whereHas('warehouse.pos', function ($q) use ($pos) {
            $q->where('posCode', $pos);
        });
    }
}
