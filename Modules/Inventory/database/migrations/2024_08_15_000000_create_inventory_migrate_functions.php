<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Inventory\app\Constants\ItemAction;
use Modules\Inventory\app\Constants\TemStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tạo function để xử lý batch import
        DB::statement($this->createBatchImportFunction());

        // Tạo function để tính toán số lượng tồn kho
        DB::statement($this->createCalculateInventoryFunction());
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP FUNCTION IF EXISTS batch_import_inventory(jsonb);");
        DB::statement("DROP FUNCTION IF EXISTS calculate_inventory(integer, integer, integer, timestamp);");
    }

    /**
     * Tạo function để xử lý batch import
     */
    private function createBatchImportFunction(): string
    {
        return <<<SQL
CREATE OR REPLACE FUNCTION batch_import_inventory(data jsonb)
RETURNS TABLE (
    item_id integer,
    product_id integer,
    tem_id integer,
    warehouse_id integer,
    transfer_id integer,
    product_transfer_id integer
) AS $$
DECLARE
    item_record record;
    new_transfer_id integer;
    new_product_transfer_id integer;
    new_item_id integer;
    new_product_id integer;
    new_tem_id integer;
    new_warehouse_id integer;
    supplier_id integer;
    action_type integer;
    qty_value integer;
BEGIN
    -- Lặp qua từng item trong dữ liệu JSON
    FOR item_record IN SELECT * FROM jsonb_to_recordset(data) AS x(
        product_code text,
        product_name text,
        tem_code text,
        warehouse_code text,
        warehouse_name text,
        supplier_code text,
        supplier_name text,
        qty integer,
        status integer,
        action integer,
        handover_date timestamp,
        actual_date timestamp,
        warranty_date timestamp,
        allocation_date timestamp,
        note text,
        branch_code text,
        tem_type integer
    )
    LOOP
        -- Tạo mã sản phẩm mới dựa trên mã cũ
        DECLARE
            old_code text := item_record.product_code;
            new_code text := 'PRD-' || upper(substring(md5(item_record.product_code) from 1 for 8));
            default_unit_id integer;
        BEGIN
            -- Tìm unit mặc định (Chiếc)
            SELECT id INTO default_unit_id FROM units WHERE unit_name = 'Chiếc' LIMIT 1;

            -- Nếu không tìm thấy, tạo mới unit mặc định
            IF default_unit_id IS NULL THEN
                INSERT INTO units (unit_code, unit_name, base_unit, operator, operation_value, is_active, created_at, updated_at)
                VALUES ('U001', 'Chiếc', NULL, '*', 1, true, NOW(), NOW())
                RETURNING id INTO default_unit_id;
            END IF;

            -- Tìm hoặc tạo mới product
            SELECT id INTO new_product_id FROM products WHERE code = new_code LIMIT 1;
            IF new_product_id IS NULL THEN
                INSERT INTO products (name, code, type, unit_id, purchase_unit_id, sale_unit_id, is_active, meta_description, created_at, updated_at)
                VALUES (item_record.product_name, new_code, 1, default_unit_id, default_unit_id, default_unit_id, 1, 'Migrated from old system. Old code: ' || old_code, NOW(), NOW())
                RETURNING id INTO new_product_id;
            END IF;
        END;

        -- Tìm hoặc tạo mới warehouse
        SELECT id INTO new_warehouse_id FROM warehouses WHERE code = item_record.warehouse_code LIMIT 1;
        IF new_warehouse_id IS NULL THEN
            INSERT INTO warehouses (name, code, is_active, created_at, updated_at)
            VALUES (item_record.warehouse_name, item_record.warehouse_code, 1, NOW(), NOW())
            RETURNING id INTO new_warehouse_id;
        END IF;

        -- Tìm hoặc tạo mới supplier nếu có
        IF item_record.supplier_code IS NOT NULL THEN
            SELECT id INTO supplier_id FROM suppliers WHERE name = item_record.supplier_code LIMIT 1;
            IF supplier_id IS NULL THEN
                INSERT INTO suppliers (name, type, is_active, created_at, updated_at)
                VALUES (item_record.supplier_name, 1, 1, NOW(), NOW())
                RETURNING id INTO supplier_id;
            END IF;
        ELSE
            supplier_id := NULL;
        END IF;

        -- Tìm hoặc tạo mới tem (nếu có)
        IF item_record.tem_code IS NOT NULL AND item_record.tem_code != '' THEN
            SELECT id INTO new_tem_id FROM product_tems WHERE code = item_record.tem_code LIMIT 1;
            IF new_tem_id IS NULL THEN
                INSERT INTO product_tems (code, branch_code, type, created_at, updated_at)
                VALUES (item_record.tem_code, item_record.branch_code, item_record.tem_type, NOW(), NOW())
                RETURNING id INTO new_tem_id;
            END IF;
        ELSE
            new_tem_id := NULL;
        END IF;

        -- Tìm hoặc tạo mới item
        BEGIN
            -- Nếu có tem, tìm item theo tem
            IF new_tem_id IS NOT NULL THEN
                SELECT id INTO new_item_id FROM product_items WHERE tem_id = new_tem_id LIMIT 1;
                IF new_item_id IS NOT NULL THEN
                    -- Cập nhật thông tin nếu cần
                    UPDATE product_items
                    SET warehouse_id = new_warehouse_id,
                        status = COALESCE(item_record.status, 1),
                        qty = COALESCE(item_record.qty, 1),
                        updated_at = NOW()
                    WHERE id = new_item_id;
                END IF;
            END IF;

            -- Nếu không tìm thấy item hoặc không có tem, tạo mới
            -- Tạo mã item_code mới
            DECLARE
                item_prefix text := 'ITEM-';
                random_part text := upper(substring(md5(random()::text) from 1 for 8));
                generated_item_code text := item_prefix || random_part;
            BEGIN
                -- Kiểm tra xem mã đã tồn tại chưa, nếu có thì tạo mã mới
                WHILE EXISTS (SELECT 1 FROM product_items WHERE code = generated_item_code) LOOP
                    random_part := upper(substring(md5(random()::text) from 1 for 8));
                    generated_item_code := item_prefix || random_part;
                END LOOP;

                -- Tạo item mới với mã đã sinh
                INSERT INTO product_items (code, product_id, warehouse_id, supplier_id, tem_id, status, qty, created_at, updated_at)
                VALUES (generated_item_code, new_product_id, new_warehouse_id, supplier_id, new_tem_id, COALESCE(item_record.status, 1), COALESCE(item_record.qty, 1), NOW(), NOW())
                RETURNING id INTO new_item_id;
            END;
        END;

        -- Xác định action type
        action_type := COALESCE(item_record.action, 6); -- Default to INVENTORY
        qty_value := COALESCE(item_record.qty, 1);

        -- Tạo transfer record
        INSERT INTO transfers (
            reference_no, user_id, type, status, to_warehouse_id, handover_date,
            acceptance_date, actual_date, total_qty, total_tax, total_cost,
            grand_total, note, supplier_id, created_at, updated_at
        )
        VALUES (
            'MIG' || to_char(NOW(), 'YYYYMMDD') || floor(random() * 9000 + 1000)::text,
            1, -- Default user_id
            action_type,
            1, -- STATUS_COMPLETED
            new_warehouse_id,
            COALESCE(item_record.handover_date, NOW()),
            COALESCE(item_record.acceptance_date, NOW()),
            COALESCE(item_record.actual_date, NOW()),
            qty_value,
            0, -- total_tax
            0, -- total_cost
            0, -- grand_total
            'Migrated from old system',
            supplier_id,
            NOW(),
            NOW()
        )
        RETURNING id INTO new_transfer_id;

        -- Tạo product_transfer record
        INSERT INTO product_transfer (
            transfer_id, product_id, tem_id, item_id, qty, action,
            to_warehouse_id, handover_date, acceptance_date, actual_date,
            to_actual_qty, from_actual_qty, supplier_id, user_id,
            warranty_date, allocation_date, created_at, updated_at
        )
        VALUES (
            new_transfer_id,
            new_product_id,
            new_tem_id, -- Có thể là NULL nếu không có tem
            new_item_id,
            qty_value,
            action_type,
            new_warehouse_id,
            COALESCE(item_record.handover_date, NOW()),
            COALESCE(item_record.acceptance_date, NOW()),
            COALESCE(item_record.actual_date, NOW()),
            qty_value,
            CASE WHEN action_type = 6 THEN 0 ELSE qty_value END, -- 6 = INVENTORY
            supplier_id,
            1, -- Default user_id
            item_record.warranty_date,
            item_record.allocation_date,
            NOW(),
            NOW()
        )
        RETURNING id INTO new_product_transfer_id;

        -- Return the created IDs
        item_id := new_item_id;
        product_id := new_product_id;
        tem_id := new_tem_id;
        warehouse_id := new_warehouse_id;
        transfer_id := new_transfer_id;
        product_transfer_id := new_product_transfer_id;

        RETURN NEXT;
    END LOOP;

    RETURN;
END;
$$ LANGUAGE plpgsql;
SQL;
    }

    /**
     * Tạo function để tính toán số lượng tồn kho
     */
    private function createCalculateInventoryFunction(): string
    {
        $positiveActions = implode(',', ItemAction::getPositiveActions());
        $negativeActions = implode(',', ItemAction::getNegativeActions());

        return <<<SQL
CREATE OR REPLACE FUNCTION calculate_inventory(p_product_id integer, p_warehouse_id integer, p_tem_id integer, p_date timestamp)
RETURNS integer AS $$
DECLARE
    incoming integer;
    outgoing integer;
    current_stock integer;
BEGIN
    -- Tính số lượng nhập kho
    SELECT COALESCE(SUM(qty), 0) INTO incoming
    FROM product_transfer
    WHERE product_id = p_product_id
      AND to_warehouse_id = p_warehouse_id
      AND (p_tem_id IS NULL OR tem_id = p_tem_id)
      AND action IN ($positiveActions)
      AND actual_date <= p_date;

    -- Tính số lượng xuất kho
    SELECT COALESCE(SUM(qty), 0) INTO outgoing
    FROM product_transfer
    WHERE product_id = p_product_id
      AND from_warehouse_id = p_warehouse_id
      AND (p_tem_id IS NULL OR tem_id = p_tem_id)
      AND action IN ($negativeActions)
      AND actual_date <= p_date;

    -- Tính số lượng tồn kho
    current_stock := incoming - outgoing;

    RETURN current_stock;
END;
$$ LANGUAGE plpgsql;
SQL;
    }
};
