<?php

namespace App\Providers;

use App\Branch;
use App\Policies\BranchPolicy;
use App\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        Branch::class => BranchPolicy::class,
    ];

    /**
     * Register any application authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        // begin: @role()
        \Blade::directive('role', function ($expression) {
            return "<?php if (\\Auth::user()->hasRole({$expression})) : ?>";
        });

        \Blade::directive('endrole', function ($expression) {
            return "<?php endif; ?>";
        });
        // begin: @endrole()

        // begin: @permission()
        \Blade::directive('permission', function ($expression) {
            $expression = str_replace(['[', ']',"'",'"'], '', $expression);
            $permissions = array_map('trim', explode(',', $expression));
            $allRequired = 'true';
            if (strtolower(end($permissions)) === 'false' || strtolower(end($permissions)) === 'true') {
                $allRequired = array_pop($permissions);
            }
            $permissionsString = json_encode($permissions);
            return "<?php if(auth()->check() && auth()->user()->hasPermission($permissionsString, $allRequired)) : ?>";
        });

        \Blade::directive('endpermission', function () {
            return "<?php endif; ?>";
        });


        // end: @endpermission

        \Gate::define('manage-session', function (User $user, $session) {
            if ($user->hasPermission('user.view')) {
                return true;
            }

            return (int)$user->id === (int)$session->user_id;
        });

        $this->registerPolicies();

    }


}
